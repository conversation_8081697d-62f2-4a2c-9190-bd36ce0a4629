// Função para calcular a semana atua
function getCurrentWeekRange() {
  const hoje = new Date();
  let diaSemana = hoje.getDay();

  // Se for domingo, vamos tratar como 7 para que a semana atual seja a anterior (segunda a sábado)
  diaSemana = (diaSemana === 0) ? 7 : diaSemana;

  // Calcula a data da última segunda-feira (início da semana)
  const monday = new Date(hoje);
  monday.setDate(hoje.getDate() - (diaSemana));

  // Calcula a data do sábado dessa mesma semana (segunda + 5 dias)
  const saturday = new Date(monday);
  saturday.setDate(monday.getDate() + 6);

  return [monday, saturday];
}

function formatDateToISO(dateStr) {
  const [day, month, year] = dateStr.split("/");
  return `${year}-${month}-${day}`;
}

document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM completamente carregado e analisado'); // Log para verificar se o DOM foi carregado

  // Sidebar toggle
  const sidebarCollapse = document.getElementById('sidebarCollapse');
  if (sidebarCollapse) {
    sidebarCollapse.addEventListener('click', function () {
      document.getElementById('sidebar').classList.toggle('active');
    });
  }

  // 1. Seleciona o elemento de conteúdo (usado para injetar HTML parcial)
  const contentWrapper = document.querySelector('.content-wrapper .content');
  const loadingOverlay = document.getElementById('loadingOverlay');

  // 2. Pega a aba ativa via querystring (ex.: ?tab=xxx)
  const urlParams = new URLSearchParams(window.location.search);
  const activeTab = urlParams.get('tab') || 'cadastrar-conta';  // Se "cadastrar-conta" é o ID da primeira aba
  const tabElement = document.querySelector(`#tab-${activeTab}`);
  const tabContent = document.querySelector(`#${activeTab}`);

  if (tabElement && tabContent) {
    // Remove classes de todas as abas e ativa só a selecionada
    document.querySelectorAll('.nav-link').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(content => content.classList.remove('show', 'active'));
    tabElement.classList.add('active');
    tabContent.classList.add('show', 'active');
  }

  // 3. Inicializa todos os eventos ao carregar a página
  initializePageEvents();

  // ============================================
  // 1. Função principal para gerenciar os links
  // ============================================
  function initializePageEvents() {
    console.log('Função initializePageEvents chamada'); // Log para verificar se a função foi chamada

    // porque a listagem de contas agora é feita via Jinja, sem fetch
    const links = [
      {
        id: 'fornecedores-link',
        url: '/contas_fornecedores',
        init: initializeFornecedoresEvents,
        load: loadFornecedores
      },
      {
        id: 'funcionarios-link',
        url: '/contas_funcionarios',
        init: initializeFuncionariosEvents,
        load: loadFuncionarios
      },
      {
        id: 'empresas-link',
        url: '/contas_empresas',
        init: initializeEmpresasEvents,
        load: loadEmpresas
      },
      {
        id: 'contas-link',
        url: '/contas_cadastro',
        init: initializeCadastroEvents
        // Carrega a aba de "Cadastrar Conta" (também contém a listagem via Jinja).
      },
      {
        id: 'dadosbancarios-link',
        url: '/contas_dadosbancarios',
        init: initializeDadosBancariosEvents,
        load: loadFornecedoresForDropdown
      },
      {
        id: 'recorrencia-link',
        url: '/contas/recorrentes',
        init: initializeRecorrenciaEvents
      },
      {
        id: 'vencimentos-link',
        url: '/contas_vencimentos',
        init: initializeContasVencimentosEvents
      },
      {
        id: 'pagamentos-link',
        url: '/contas/pagamentos',
        init: initializePagamentosEvents
      }
    ];

    // Percorre cada link e adiciona evento de "click" p/ fetchPage
    links.forEach(link => {
      const linkElement = document.getElementById(link.id);
      if (linkElement) {
        linkElement.addEventListener('click', function (event) {
          event.preventDefault();
          fetchPage(link.url, link.init);
        });
      }
    });

    addDeleteEventListeners();
    addEditEventListeners();
    addEditFornEventListeners();
  }

  // ============================================
  // 2. fetchPage: carrega HTML parcial e injeta
  // ============================================
  function fetchPage(url, callback) {
    console.log("Front-end log: Iniciando fetch da URL:", url);
    showLoading();
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao carregar a página: ${url}`);
        }
        return response.text();
      })
      .then(html => {
        console.log("Front-end log: HTML recebido com sucesso.");
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newContent = doc.querySelector('.content-wrapper .content');

        if (newContent) {
          contentWrapper.innerHTML = newContent.innerHTML;
          if (callback) {
            callback();
          }
        } else {
          console.error('Erro ao encontrar o novo conteúdo.');
        }
      })
      .catch(error => console.error("Front-end log: Erro ao carregar conteúdo:", error))
      .finally(hideLoading);
  }

  // ========================================================
  // 3. Funções de carregamento específico (Tabela / Select)
  // ========================================================

  // 3.1 Tabela de Fornecedores
  function loadFornecedores() {
    console.log('Carregando Fornecedores...');
    fetch('/get_fornecedores')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar fornecedores: ${response.statusText}`);
        }
        return response.json();
      })
      .then(fornecedores => {
        const tabela = document.getElementById('fornecedores-list');
        if (tabela) {
          tabela.addEventListener('click', function (event) {
            const botao = event.target.closest('.btn-edit-fornecedor');
            if (botao) {
              let fornecedorId = botao.getAttribute('data-id');
              if (!fornecedorId) {
                const parentRow = botao.closest('tr');
                if (parentRow) {
                  fornecedorId = parentRow.getAttribute('data-conta-id');
                }
              }
              if (!fornecedorId || fornecedorId === 'null') {
                console.error('Fornecedor ID não encontrado.');
                return;
              }
              fetch(`/get_fornecedores/${fornecedorId}`)
                .then(response => {
                  if (!response.ok) {
                    throw new Error('Fornecedor não encontrado');
                  }
                  return response.json();
                })
                .then(fornecedor => {
                  openEditModalForn(fornecedor);
                })
                .catch(error => console.error('Erro ao carregar dados do fornecedor:', error));
            }
          });
        }
        tabela.innerHTML = '';

        if (fornecedores.length === 0) {
          tabela.innerHTML = '<tr><td colspan="10">Nenhum fornecedor cadastrado.</td></tr>';
          return;
        }

        fornecedores.forEach(f => {
          const linha = document.createElement('tr');
          linha.innerHTML = `
                <td>${f.id}</td>
                <td>${renderTexto(f.nome)}</td>
                <td>${f.cnpj_cpf}</td>
                <td>${renderTexto(f.endereco)}</td>
                <td>${f.telefone}</td>
                <td>${renderEmail(f.email)}</td>
                <td>${f.categoria}</td>
                <td>${renderTexto(f.site)}</td>
                <td>${renderTexto(f.observacoes)}</td>
                <td style="padding: 5px; text-align: center;">
                  <button class="btn btn-info btn-edit-fornecedor btn-sm" data-id="${f.id}">
                    <i class="fa-solid fa-pen-to-square"></i>
                  </button>
                </td>
            `;
          tabela.appendChild(linha);
        });
        const nomesUnicos = [...new Set(fornecedores.map(f => f.nome))];
        const filtroSelect = document.getElementById('filtro-nome-fornecedor');
        if (filtroSelect) {
          filtroSelect.innerHTML = '<option value="">Todos os Fornecedores</option>';
          nomesUnicos.forEach(nome => {
            if (nome) {
              const option = document.createElement('option');
              option.value = nome;
              option.textContent = nome;
              filtroSelect.appendChild(option);
            }
          });
          filtroSelect.addEventListener('change', function () {
            const selected = this.value.toLowerCase();
            const rows = document.querySelectorAll('#fornecedores-list tr');
            rows.forEach(row => {
              const nomeColuna = row.querySelector('td:nth-child(2)');
              if (!nomeColuna) return;
              row.style.display = (!selected || nomeColuna.textContent.toLowerCase().includes(selected)) ? '' : 'none';
            });
          });
        }
        console.log('Fornecedores carregados.');
      })
      .catch(error => console.error('Erro ao carregar fornecedores:', error))
      .finally(hideLoading);
  }

  function openEditModalForn(fornecedor) {
    // Preenche os campos do modal com os dados da fornecedor
    document.getElementById('editar-fornecedor-id').value = fornecedor.id;
    document.getElementById('editar-nome-fornecedor').value = fornecedor.nome;
    document.getElementById('editar-documento-fornecedor').value = fornecedor.cnpj_cpf;
    document.getElementById('editar-endereco-fornecedor').value = fornecedor.endereco;
    document.getElementById('editar-telefone-fornecedor').value = fornecedor.telefone;
    document.getElementById('editar-email-fornecedor').value = fornecedor.email;
    document.getElementById('editar-categoria-fornecedor').value = fornecedor.categoria;
    document.getElementById('editar-site-fornecedor').value = fornecedor.site;
    document.getElementById('editar-observacoes-fornecedor').value = fornecedor.observacoes;
    // Abre o modal existente
    console.log($('#modalEditarFornecedor'));
    $('#modalEditarFornecedor').modal('show');
  }

  // Função para fechar o modal de fornecedores
  function closeModalFornecedores() {
    $('#modalEditarFornecedor').modal('hide');
  }

  // Adiciona ouvinte para botões com data-dismiss no modal
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('#modalEditarFornecedor [data-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', closeModalFornecedores);
    });
  });

  // Função para capturar corretamente o id da fornecedor ao clicar no botão "Editar"
  function addEditFornEventListeners() {
    document.querySelectorAll('.btn-edit-fornecedor').forEach(button => {
      button.addEventListener('click', function () {
        let fornecedorId = this.getAttribute('data-id');

        if (!fornecedorId) {
          const parentRow = this.closest('tr');
          if (parentRow) {
            fornecedorId = parentRow.getAttribute('data-conta-id');
            console.log('Obtido data-conta-id do TR:', fornecedorId);
          }
        }
        console.log('Fornecedor ID capturado:', fornecedorId);

        if (!fornecedorId || fornecedorId === 'null') {
          console.error('Fornecedor ID não encontrado.');
          return;
        }

        // Agora inclui os dois parâmetros na URL se necessário (ou apenas o id)
        fetch(`/get_fornecedores/${fornecedorId}`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Fornecedor não encontrado');
            }
            return response.json();
          })
          .then(fornecedor => {
            console.log('Dados do fornecedor:', fornecedor);
            openEditModalForn(fornecedor);
          })
          .catch(error => console.error('Erro ao carregar dados do fornecedor:', error));
      });
    });
  }

  // Função para salvar as alterações da conta
  function addSaveEditFornEventListener() {
    const saveButton = document.getElementById('salvar-editar-fornecedor');
    if (saveButton && !saveButton.dataset.listenerAdded) {
      saveButton.addEventListener('click', function () {
        if (confirm('Deseja salvar as alterações?')) {
          // Coleta os valores dos campos do modal
          const fornecedorId = document.getElementById('editar-fornecedor-id').value;
          const nome = document.getElementById('editar-nome-fornecedor').value;
          const documento = document.getElementById('editar-documento-fornecedor').value;
          const endereco = document.getElementById('editar-endereco-fornecedor').value;
          const telefone = document.getElementById('editar-telefone-fornecedor').value;
          const email = document.getElementById('editar-email-fornecedor').value;
          const categoria = document.getElementById('editar-categoria-fornecedor').value;
          const site = document.getElementById('editar-site-fornecedor').value;
          const observacoes = document.getElementById('editar-observacoes-fornecedor').value;

          const data = {
            nome: nome,
            cnpj_cpf: documento,
            endereco: endereco,
            telefone: telefone,
            email: email,
            categoria: categoria,
            site: site,
            observacoes: observacoes
          };

          fetch(`/update_fornecedor/${fornecedorId}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })
            .then(response => response.json())
            .then(result => {
              console.log('Resposta do servidor:', result);
              if (result.message) {
                alert('Fornecedor atualizada com sucesso!');
                location.reload();
              } else {
                alert('Erro ao atualizar fornecedor: ' + result.error);
              }
            })
            .catch(error => {
              console.error('Erro ao atualizar a fornecedor:', error);
              alert('Erro ao atualizar a fornecedor.');
            });
        }
      });
      // Marca que o listener já foi adicionado para não adicionar novamente
      saveButton.dataset.listenerAdded = "true";
    }
  }

  // Tabela de Funcionarios
  function loadFuncionarios() {
    console.log('Carregando Funcionários...');
    fetch('/get_funcionarios')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar funcionários: ${response.statusText}`);
        }
        return response.json();
      })
      .then(funcionarios => {
        const tabela = document.getElementById('funcionario-list');
        if (!tabela) {
          console.error('Tabela de funcionários (#funcionario-list) não encontrada.');
          return;
        }
        tabela.innerHTML = '';

        if (funcionarios.length === 0) {
          tabela.innerHTML = '<tr><td colspan="10">Nenhum funcionário cadastrado.</td></tr>';
          return;
        }

        funcionarios.forEach(func => {
          const linha = document.createElement('tr');
          linha.setAttribute('data-funcionario-id', func.id);
          linha.innerHTML = `
              <td>${func.nome || '-'}</td>
              <td>${func.cpf || '-'}</td>
              <td>${func.email || '-'}</td>
              <td>${func.telefone || '-'}</td>
              <td>
                <button class="btn btn-info btn-sm btn-edit-funcionario" data-id="${func.id}">Editar</button>
              </td>
            `;
          tabela.appendChild(linha);
        });

        // Popula o dropdown de filtro com os nomes únicos dos funcionários
        const nomesUnicos = [...new Set(funcionarios.map(f => f.nome))];
        const filtroSelect = document.getElementById('filtro-nome-funcionario');
        if (filtroSelect) {
          filtroSelect.innerHTML = '<option value="">Todos os Funcionários</option>';
          nomesUnicos.forEach(nome => {
            if (nome) {
              const option = document.createElement('option');
              option.value = nome;
              option.textContent = nome;
              filtroSelect.appendChild(option);
            }
          });

          // Adiciona evento de change para filtrar a tabela
          filtroSelect.addEventListener('change', function () {
            const selected = this.value.toLowerCase();
            const rows = document.querySelectorAll('#funcionario-list tr');
            rows.forEach(row => {
              const nomeColuna = row.querySelector('td:nth-child(1)');
              if (!nomeColuna) return;
              row.style.display = (!selected || nomeColuna.textContent.toLowerCase().includes(selected)) ? '' : 'none';
            });
          });
        }

        // Adiciona eventos aos botões de editar
        addEditFuncionariosEventListeners();
        console.log('Funcionários carregados.');
      })
      .catch(error => console.error('Erro ao carregar funcionários:', error))
      .finally(hideLoading);
  }

  // Função para capturar corretamente o id do funcionário ao clicar no botão "Editar"
  function addEditFuncionariosEventListeners() {
    const buttons = document.querySelectorAll('.btn-edit-funcionario');

    buttons.forEach(button => {
      button.addEventListener('click', function (event) {
        event.preventDefault();

        let funcionarioId = this.getAttribute('data-id');

        if (!funcionarioId) {
          const parentRow = this.closest('tr');
          if (parentRow) {
            funcionarioId = parentRow.getAttribute('data-funcionario-id');
          }
        }

        if (!funcionarioId || funcionarioId === 'null') {
          console.error('ID do funcionário não encontrado.');
          return;
        }

        // Busca os dados do funcionário pelo ID
        fetch(`/get_funcionarios/${funcionarioId}`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Funcionário não encontrado');
            }
            return response.json();
          })
          .then(funcionario => {
            openEditModalFuncionario(funcionario);
          })
          .catch(error => console.error('Erro ao carregar dados do funcionário:', error));
      });
    });
  }

  // Função para abrir o modal de edição com os dados do funcionário
  function openEditModalFuncionario(funcionario) {
    try {
      console.log('Dados do funcionário recebidos:', funcionario);

      // Verifica se o modal existe
      const modal = document.getElementById('modalEditarFuncionario');
      if (!modal) {
        // Se o modal não existe, vamos criá-lo dinamicamente
        const modalHTML = `
        <div class="modal fade" id="modalEditarFuncionario" tabindex="-1" role="dialog" aria-labelledby="modalEditarFuncionarioLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="modalEditarFuncionarioLabel">Editar Funcionário</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="form-editar-funcionario">
                            <input type="hidden" id="editar-funcionario-id" name="editar-funcionario-id">

                            <!-- Linha com campos Nome e CPF -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-nome-funcionario">Nome</label>
                                        <input type="text" class="form-control" id="editar-nome-funcionario" name="editar-nome-funcionario" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-cpf-funcionario">CPF</label>
                                        <input type="text" class="form-control" id="editar-cpf-funcionario" name="editar-cpf-funcionario" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Linha com campos Email e Telefone -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-email-funcionario">Email</label>
                                        <input type="email" class="form-control" id="editar-email-funcionario" name="editar-email-funcionario" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-telefone-funcionario">Telefone</label>
                                        <input type="text" class="form-control" id="editar-telefone-funcionario" name="editar-telefone-funcionario" required>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" id="salvar-editar-funcionario">Salvar Alterações</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        // Adiciona o modal ao corpo do documento
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Inicializa o modal
        $('#modalEditarFuncionario').modal({
          backdrop: true,
          keyboard: true,
          focus: true,
          show: false
        });

        // Adiciona o evento de clique ao botão de salvar
        document.getElementById('salvar-editar-funcionario').addEventListener('click', function() {
          if (confirm('Deseja salvar as alterações?')) {
            // Coleta os valores dos campos do modal
            const funcionarioId = document.getElementById('editar-funcionario-id').value;
            const nome = document.getElementById('editar-nome-funcionario').value;
            const cpf = document.getElementById('editar-cpf-funcionario').value;
            const email = document.getElementById('editar-email-funcionario').value;
            const telefone = document.getElementById('editar-telefone-funcionario').value;

            const data = {
              nome: nome,
              cpf: cpf,
              email: email,
              telefone: telefone
            };

            fetch(`/update_funcionarios/${funcionarioId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(data)
            })
              .then(response => response.json())
              .then(result => {
                if (result.message) {
                  alert('Funcionário atualizado com sucesso!');
                  location.reload();
                } else {
                  alert('Erro ao atualizar funcionário: ' + result.error);
                }
              })
              .catch(error => {
                console.error('Erro ao atualizar o funcionário:', error);
                alert('Erro ao atualizar o funcionário.');
              });
          }
        });

        // Aplica máscara ao campo de CPF
        $('#editar-cpf-funcionario').on('input', function () {
          this.value = this.value.replace(/\D/g, '').replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        });

        // Aplica máscara ao campo de telefone
        $('#editar-telefone-funcionario').on('input', function () {
          this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        });
      }

      // Preenche os campos do modal com os dados do funcionário
      // A função get_funcionario_id retorna um objeto único, não um array
      const funcionarioData = Array.isArray(funcionario) ? funcionario[0] : funcionario;

      const idInput = document.getElementById('editar-funcionario-id');
      if (idInput) idInput.value = funcionarioData.id;

      const nomeInput = document.getElementById('editar-nome-funcionario');
      if (nomeInput) nomeInput.value = funcionarioData.nome;

      const cpfInput = document.getElementById('editar-cpf-funcionario');
      if (cpfInput) cpfInput.value = funcionarioData.cpf;

      const emailInput = document.getElementById('editar-email-funcionario');
      if (emailInput) emailInput.value = funcionarioData.email;

      const telefoneInput = document.getElementById('editar-telefone-funcionario');
      if (telefoneInput) telefoneInput.value = funcionarioData.telefone;

      // Abre o modal existente
      $('#modalEditarFuncionario').modal('show');
    } catch (error) {
      console.error('Erro ao abrir o modal:', error);
    }
  }

  // Função para fechar o modal de funcionário
  function closeModalFuncionario() {
    $('#modalEditarFuncionario').modal('hide');
  }

  // 3.2 Tabela de Dados Bancários
  function loadDadosBancarios() {
    console.log('Carregando Dados Bancários...');
    fetch('/get_dadosbancarios')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar dados bancários: ${response.statusText}`);
        }
        return response.json();
      })
      .then(dadosBancarios => {
        const tabela = document.getElementById('dados-bancarios-list');
        if (!tabela) {
          console.error('Tabela de dados bancários (#dados-bancarios-list) não encontrada.');
          return;
        }
        tabela.innerHTML = '';

        if (dadosBancarios.length === 0) {
          tabela.innerHTML = '<tr><td colspan="10">Nenhum dado bancário cadastrado.</td></tr>';
          return;
        }

        dadosBancarios.forEach(dado => {
          // Lógica para exibir Fornecedor ou Funcionário em uma mesma célula
          const beneficiario = dado.fornecedor
            ? dado.fornecedor
            : (dado.funcionario || '-');

          const linha = document.createElement('tr');
          linha.setAttribute('data-dados-bancarios-id', dado.id);
          linha.innerHTML = `
          <td>${dado.id || '-'}</td>
          <td>${dado.banco || '-'}</td>
          <td>${dado.numero_banco || '-'}</td>
          <td>${dado.agencia || '-'}</td>
          <td>${dado.digito_agencia || '-'}</td>
          <td>${dado.conta || '-'}</td>
          <td>${dado.digito_conta || '-'}</td>
          <td>${dado.tipo || '-'}</td>
          <!-- Exibimos só a variável beneficiario -->
          <td>${beneficiario}</td>
          <td>
            <button class="btn btn-info btn-sm btn-edit-dados-bancarios" data-id="${dado.id}">Editar</button>
            <button class="btn btn-danger btn-sm delete-conta" data-id="${dado.id}">Excluir</button>
          </td>
        `;
          tabela.appendChild(linha);
        });

        // Adiciona eventos aos botões de editar
        addEditDadosBancariosEventListeners();
        console.log('Dados bancários carregados.');
      })
      .catch(error => console.error('Erro ao carregar dados bancários:', error));
  }

  // Função para capturar corretamente o id dos dados bancários ao clicar no botão "Editar"
  function addEditDadosBancariosEventListeners() {
    const buttons = document.querySelectorAll('.btn-edit-dados-bancarios');

    buttons.forEach(button => {
      button.addEventListener('click', function (event) {
        event.preventDefault();

        let dadosBancariosId = this.getAttribute('data-id');

        if (!dadosBancariosId) {
          const parentRow = this.closest('tr');
          if (parentRow) {
            dadosBancariosId = parentRow.getAttribute('data-dados-bancarios-id');
          }
        }

        if (!dadosBancariosId || dadosBancariosId === 'null') {
          console.error('ID dos dados bancários não encontrado.');
          return;
        }

        // Busca os dados bancários pelo ID
        fetch(`/get_dadosbancarios_id/${dadosBancariosId}`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Dados bancários não encontrados');
            }
            return response.json();
          })
          .then(dadosBancarios => {
            openEditModalDadosBancarios(dadosBancarios);
          })
          .catch(error => console.error('Erro ao carregar dados bancários:', error));
      });
    });
  }

  // Função para abrir o modal de edição com os dados bancários
  function openEditModalDadosBancarios(dadosBancarios) {
    try {
      console.log('Dados bancários recebidos:', dadosBancarios);

      // Verifica se o modal existe
      const modal = document.getElementById('modalEditarDadosBancarios');
      if (!modal) {
        // Se o modal não existe, vamos criá-lo dinamicamente
        const modalHTML = `
        <div class="modal fade" id="modalEditarDadosBancarios" tabindex="-1" role="dialog" aria-labelledby="modalEditarDadosBancariosLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="modalEditarDadosBancariosLabel">Editar Dados Bancários</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="form-editar-dados-bancarios">
                            <input type="hidden" id="editar-dados-bancarios-id" name="editar-dados-bancarios-id">

                            <!-- Linha com campos Nome do Banco e Número do Banco -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-nome-banco">Nome do Banco</label>
                                        <input type="text" class="form-control" id="editar-nome-banco" name="editar-nome-banco" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-numero-banco">Número do Banco</label>
                                        <input type="text" class="form-control" id="editar-numero-banco" name="editar-numero-banco" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Linha com campos Agência e Dígito da Agência -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-agencia">Agência</label>
                                        <input type="text" class="form-control" id="editar-agencia" name="editar-agencia" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-digito-agencia">Dígito da Agência</label>
                                        <input type="text" class="form-control" id="editar-digito-agencia" name="editar-digito-agencia" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Linha com campos Conta e Dígito da Conta -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-conta">Conta</label>
                                        <input type="text" class="form-control" id="editar-conta" name="editar-conta" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-digito-conta">Dígito da Conta</label>
                                        <input type="text" class="form-control" id="editar-digito-conta" name="editar-digito-conta" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Campo de Tipo de Conta -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-tipo-conta">Tipo de Conta</label>
                                        <select class="form-control" id="editar-tipo-conta" name="editar-tipo-conta" required>
                                            <option value="corrente">Conta Corrente</option>
                                            <option value="poupanca">Conta Poupança</option>
                                            <option value="salario">Conta Salário</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" id="salvar-editar-dados-bancarios">Salvar Alterações</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        // Adiciona o modal ao corpo do documento
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Inicializa o modal
        $('#modalEditarDadosBancarios').modal({
          backdrop: true,
          keyboard: true,
          focus: true,
          show: false
        });

        // Adiciona o evento de clique ao botão de salvar
        document.getElementById('salvar-editar-dados-bancarios').addEventListener('click', function() {
          if (confirm('Deseja salvar as alterações?')) {
            // Coleta os valores dos campos do modal
            const dadosBancariosId = document.getElementById('editar-dados-bancarios-id').value;
            const nomeBanco = document.getElementById('editar-nome-banco').value;
            const numeroBanco = document.getElementById('editar-numero-banco').value;
            const agencia = document.getElementById('editar-agencia').value;
            const digitoAgencia = document.getElementById('editar-digito-agencia').value;
            const conta = document.getElementById('editar-conta').value;
            const digitoConta = document.getElementById('editar-digito-conta').value;
            const tipoConta = document.getElementById('editar-tipo-conta').value;

            const data = {
              nome_banco: nomeBanco,
              numero_banco: numeroBanco,
              agencia: agencia,
              digito_agencia: digitoAgencia,
              conta: conta,
              digito_conta: digitoConta,
              tipo_conta: tipoConta
            };

            fetch(`/update_dadosbancarios/${dadosBancariosId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(data)
            })
              .then(response => response.json())
              .then(result => {
                if (result.message) {
                  alert('Dados bancários atualizados com sucesso!');
                  location.reload();
                } else {
                  alert('Erro ao atualizar dados bancários: ' + result.error);
                }
              })
              .catch(error => {
                console.error('Erro ao atualizar os dados bancários:', error);
                alert('Erro ao atualizar os dados bancários.');
              });
          }
        });
      }

      // Preenche os campos do modal com os dados bancários
      // A função get_dados_bancarios_id retorna um array com um objeto, precisamos pegar o primeiro item
      const dadosBancariosData = Array.isArray(dadosBancarios) ? dadosBancarios[0] : dadosBancarios;

      const idInput = document.getElementById('editar-dados-bancarios-id');
      if (idInput) idInput.value = dadosBancariosData.id;

      const nomeBancoInput = document.getElementById('editar-nome-banco');
      if (nomeBancoInput) nomeBancoInput.value = dadosBancariosData.banco;

      const numeroBancoInput = document.getElementById('editar-numero-banco');
      if (numeroBancoInput) numeroBancoInput.value = dadosBancariosData.numero_banco;

      const agenciaInput = document.getElementById('editar-agencia');
      if (agenciaInput) agenciaInput.value = dadosBancariosData.agencia;

      const digitoAgenciaInput = document.getElementById('editar-digito-agencia');
      if (digitoAgenciaInput) digitoAgenciaInput.value = dadosBancariosData.digito_agencia;

      const contaInput = document.getElementById('editar-conta');
      if (contaInput) contaInput.value = dadosBancariosData.conta;

      const digitoContaInput = document.getElementById('editar-digito-conta');
      if (digitoContaInput) digitoContaInput.value = dadosBancariosData.digito_conta;

      const tipoContaInput = document.getElementById('editar-tipo-conta');
      if (tipoContaInput) tipoContaInput.value = dadosBancariosData.tipo;

      // Abre o modal existente
      $('#modalEditarDadosBancarios').modal('show');
    } catch (error) {
      console.error('Erro ao abrir o modal:', error);
    }
  }

  // Função para fechar o modal de dados bancários
  function closeModalDadosBancarios() {
    $('#modalEditarDadosBancarios').modal('hide');
  }

  // 3.3 Tabela de Empresas
  function loadEmpresas() {
    console.log('Carregando Empresas...');
    fetch('/get_empresas')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar empresas: ${response.statusText}`);
        }
        return response.json();
      })
      .then(empresas => {
        const tabela = document.getElementById('empresas-list');
        if (!tabela) {
          console.error('Tabela de empresas (#empresas-list) não encontrada.');
          return;
        }
        tabela.innerHTML = '';

        if (empresas.length === 0) {
          tabela.innerHTML = '<tr><td colspan="8">Nenhuma empresa cadastrada.</td></tr>';
          return;
        }

        empresas.forEach(emp => {
          const linha = document.createElement('tr');
          linha.setAttribute('data-empresa-id', emp.id);
          linha.innerHTML = `
              <td>${emp.id || '-'}</td>
              <td>${emp.nome_empresa || '-'}</td>
              <td>${emp.cnpj || '-'}</td>
              <td>${emp.endereco || '-'}</td>
              <td>${emp.telefone || '-'}</td>
              <td>${emp.email || '-'}</td>
              <td>${emp.observacoes || '-'}</td>
              <td>
                <button class="btn btn-info btn-sm btn-edit-empresa" data-id="${emp.id}">Editar</button>
              </td>
            `;
          tabela.appendChild(linha);
        });
        const nomesUnicos = [...new Set(empresas.map(e => e.nome_empresa))];
        const filtroSelect = document.getElementById('filtro-nome-empresa');
        if (filtroSelect) {
          filtroSelect.innerHTML = '<option value="">Todas as Empresas</option>';
          nomesUnicos.forEach(nome => {
            if (nome) {
              const option = document.createElement('option');
              option.value = nome;
              option.textContent = nome;
              filtroSelect.appendChild(option);
            }
          });
          filtroSelect.addEventListener('change', function () {
            const selected = this.value.toLowerCase();
            const rows = document.querySelectorAll('#empresas-list tr');
            rows.forEach(row => {
              const nomeColuna = row.querySelector('td:nth-child(2)');
              if (!nomeColuna) return;
              row.style.display = (!selected || nomeColuna.textContent.toLowerCase().includes(selected)) ? '' : 'none';
            });
          });
        }

        // Adiciona eventos aos botões de editar
        addEditEmpresaEventListeners();
        console.log('Empresas carregadas.');
      })
      .catch(error => console.error('Erro ao carregar empresas:', error))
      .finally(hideLoading);
  }

  // Função para abrir o modal de edição com os dados da empresa
  function openEditModalEmpresa(empresa) {
    try {
      // Verifica se o modal existe
      const modal = document.getElementById('modalEditarEmpresa');
      if (!modal) {
        // Se o modal não existe, vamos criá-lo dinamicamente
        const modalHTML = `
        <div class="modal fade" id="modalEditarEmpresa" tabindex="-1" role="dialog" aria-labelledby="modalEditarEmpresaLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="modalEditarEmpresaLabel">Editar Empresa</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="form-editar-empresa">
                            <input type="hidden" id="editar-empresa-id" name="editar-empresa-id">

                            <!-- Linha com campos Nome da Empresa e CNPJ -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-nome-empresa">Nome da Empresa</label>
                                        <input type="text" class="form-control" id="editar-nome-empresa" name="editar-nome-empresa" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-cnpj-empresa">CNPJ</label>
                                        <input type="text" class="form-control" id="editar-cnpj-empresa" name="editar-cnpj-empresa" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Linha com campos Endereço e Telefone -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-endereco-empresa">Endereço</label>
                                        <input type="text" class="form-control" id="editar-endereco-empresa" name="editar-endereco-empresa" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-telefone-empresa">Telefone</label>
                                        <input type="text" class="form-control" id="editar-telefone-empresa" name="editar-telefone-empresa" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Campo de Email -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editar-email-empresa">Email</label>
                                        <input type="email" class="form-control" id="editar-email-empresa" name="editar-email-empresa" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Campo de Observações -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="editar-observacoes-empresa">Observações</label>
                                        <textarea class="form-control" id="editar-observacoes-empresa" name="editar-observacoes-empresa" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" id="salvar-editar-empresa">Salvar Alterações</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        // Adiciona o modal ao corpo do documento
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Inicializa o modal
        $('#modalEditarEmpresa').modal({
          backdrop: true,
          keyboard: true,
          focus: true,
          show: false
        });

        // Adiciona o evento de clique ao botão de salvar
        document.getElementById('salvar-editar-empresa').addEventListener('click', function() {
          if (confirm('Deseja salvar as alterações?')) {
            // Coleta os valores dos campos do modal
            const empresaId = document.getElementById('editar-empresa-id').value;
            const nome = document.getElementById('editar-nome-empresa').value;
            const cnpj = document.getElementById('editar-cnpj-empresa').value;
            const endereco = document.getElementById('editar-endereco-empresa').value;
            const telefone = document.getElementById('editar-telefone-empresa').value;
            const email = document.getElementById('editar-email-empresa').value;
            const observacoes = document.getElementById('editar-observacoes-empresa').value;

            const data = {
              nome_empresa: nome,
              cnpj: cnpj,
              endereco: endereco,
              telefone: telefone,
              email: email,
              observacoes: observacoes
            };

            fetch(`/update_empresa/${empresaId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(data)
            })
              .then(response => response.json())
              .then(result => {
                if (result.message) {
                  alert('Empresa atualizada com sucesso!');
                  location.reload();
                } else {
                  alert('Erro ao atualizar empresa: ' + result.error);
                }
              })
              .catch(error => {
                console.error('Erro ao atualizar a empresa:', error);
                alert('Erro ao atualizar a empresa.');
              });
          }
        });

        // Aplica máscaras aos campos
        $('#editar-cnpj-empresa').on('input', function () {
          this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
        });

        $('#editar-telefone-empresa').on('input', function () {
          this.value = this.value.replace(/\D/g, '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
        });
      }

      // Preenche os campos do modal com os dados da empresa
      const idInput = document.getElementById('editar-empresa-id');
      if (idInput) idInput.value = empresa.id;

      const nomeInput = document.getElementById('editar-nome-empresa');
      if (nomeInput) nomeInput.value = empresa.nome_empresa;

      const cnpjInput = document.getElementById('editar-cnpj-empresa');
      if (cnpjInput) cnpjInput.value = empresa.cnpj;

      const enderecoInput = document.getElementById('editar-endereco-empresa');
      if (enderecoInput) enderecoInput.value = empresa.endereco;

      const telefoneInput = document.getElementById('editar-telefone-empresa');
      if (telefoneInput) telefoneInput.value = empresa.telefone;

      const emailInput = document.getElementById('editar-email-empresa');
      if (emailInput) emailInput.value = empresa.email;

      const observacoesInput = document.getElementById('editar-observacoes-empresa');
      if (observacoesInput) observacoesInput.value = empresa.observacoes;

      // Abre o modal existente
      $('#modalEditarEmpresa').modal('show');
    } catch (error) {
      console.error('Erro ao abrir o modal:', error);
    }
  }

  // Função para fechar o modal de empresas
  function closeModalEmpresa() {
    $('#modalEditarEmpresa').modal('hide');
  }

  // Adiciona ouvinte para botões com data-dismiss no modal
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('#modalEditarEmpresa [data-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', closeModalEmpresa);
    });
  });

  // Função para capturar corretamente o id da empresa ao clicar no botão "Editar"
  function addEditEmpresaEventListeners() {
    const buttons = document.querySelectorAll('.btn-edit-empresa');

    buttons.forEach(button => {
      button.addEventListener('click', function (event) {
        event.preventDefault();

        let empresaId = this.getAttribute('data-id');

        if (!empresaId) {
          const parentRow = this.closest('tr');
          if (parentRow) {
            empresaId = parentRow.getAttribute('data-empresa-id');
          }
        }

        if (!empresaId || empresaId === 'null') {
          console.error('Empresa ID não encontrado.');
          return;
        }

        // Busca os dados da empresa pelo ID
        fetch(`/get_empresas/${empresaId}`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Empresa não encontrada');
            }
            return response.json();
          })
          .then(empresa => {
            openEditModalEmpresa(empresa);
          })
          .catch(error => console.error('Erro ao carregar dados da empresa:', error));
      });
    });
  }



  // 3.4 Fornecedores (SELECT) -> para o form de cadastro
  function loadFornecedoresForDropdown() {
    fetch('/get_fornecedores')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar fornecedores: ${response.statusText}`);
        }
        return response.json();
      })
      .then(fornecedores => {
        const dropdown = document.getElementById('fornecedor');
        if (!dropdown) {
          console.error('Campo #fornecedor não encontrado para SELECT.');
          return;
        }
        dropdown.innerHTML = '<option value="" disabled selected>Selecione o fornecedor</option>';

        fornecedores.forEach(f => {
          const option = document.createElement('option');
          option.value = f.id;
          option.textContent = f.nome;
          dropdown.appendChild(option);
        });
      })
      .catch(error => console.error('Erro ao carregar fornecedores (SELECT):', error));
  }

  // 3.5 Empresas (SELECT) -> Para "empresa-pagadora"
  function loadEmpresasForDropdown() {
    fetch('/get_empresas')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Erro ao buscar empresas: ${response.statusText}`);
        }
        return response.json();
      })
      .then(empresas => {
        const selectEmpresa = document.getElementById('empresa-pagadora');
        if (!selectEmpresa) {
          console.error('#empresa-pagadora não encontrado.');
          return;
        }
        // Limpa as opções atuais e adiciona a opção "Selecione"
        selectEmpresa.innerHTML = '<option value="" disabled selected>Selecione a empresa</option>';

        empresas.forEach(e => {
          const option = document.createElement('option');
          option.value = e.id;
          option.textContent = e.nome_empresa;
          selectEmpresa.appendChild(option);
        });
      })
      .catch(error => console.error('Erro ao carregar empresas (SELECT):', error));
  }

  // ===============================================
  // 4. Funções auxiliares (feedback, loading, etc.)
  // ===============================================
  function flashMessage(message, category) {
    const container = document.getElementById('feedback-container') || document.createElement('div');
    container.id = 'feedback-container';
    container.className = 'feedback-container';
    document.body.prepend(container);

    const alert = document.createElement('div');
    alert.className = `alert alert-${category} alert-dismissible fade show`;
    alert.role = 'alert';
    alert.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      `;
    container.appendChild(alert);

    setTimeout(() => {
      alert.classList.remove('show');
      alert.classList.add('hide');
      setTimeout(() => alert.remove(), 1000);
    }, 5000);
  }

  function showLoading() {
    if (loadingOverlay) {
      loadingOverlay.style.display = 'flex';
    }
  }

  function hideLoading() {
    if (loadingOverlay) {
      loadingOverlay.style.display = 'none';
    }
  }

  // Função para deletar conta
  function deleteConta(contaId) {
    if (confirm('Tem certeza de que deseja excluir esta conta?')) {
      fetch(`/delete_conta/${contaId}`, {
        method: 'DELETE'
      })
        .then(response => {
          if (!response.ok) {
            throw new Error('Erro ao deletar a conta');
          }
          return response.json();
        })
        .then(result => {
          if (result.message) {
            flashMessage(result.message, 'success');
            document.querySelector(`button[data-id="${contaId}"]`).closest('tr').remove();
          } else {
            flashMessage(result.error, 'danger');
          }
        })
        .catch(error => {
          console.error('Erro ao deletar a conta:', error);
          flashMessage('Erro inesperado ao deletar a conta.', 'danger');
        });
    }
  }

  // Adiciona evento de clique ao botão "Excluir"
  function addDeleteEventListeners() {
    document.querySelectorAll('.delete-conta').forEach(button => {
      button.addEventListener('click', function () {
        const contaId = this.getAttribute('data-id');
        deleteConta(contaId);
      });
    });
  }

  // Função para abrir o modal de edição com os dados da conta
  function openEditModal(conta) {
    // Preenche os campos do modal com os dados da conta
    document.getElementById('editar-conta-id').value = conta.id;
    document.getElementById('editar-recorrencia-id').value = conta.recorrencia_id;
    document.getElementById('editar-valor-conta').value = formatarValor(conta.valor);
    document.getElementById('editar-data-vencimento').value = conta.data_vencimento;
    document.getElementById('editar-codigo-barras').value = conta.codigo_barras;
    document.getElementById('editar-observacoes').value = conta.observacoes;
    document.getElementById('editar-tipo_conta').value = conta.tipo_conta;

    // Adiciona evento de upload de boleto SEMPRE que abrir o modal
    const editarUploadBoletoInput = document.getElementById('editar-upload-boleto');
    const editarCodigoBarrasInput = document.getElementById('editar-codigo-barras');
    const spinner = document.getElementById('loadingOverlay');
    if (editarUploadBoletoInput) {
      // Remove event listeners antigos
      const newInput = editarUploadBoletoInput.cloneNode(true);
      editarUploadBoletoInput.parentNode.replaceChild(newInput, editarUploadBoletoInput);
      // Atualiza o label ao selecionar arquivo
      newInput.addEventListener('change', function () {
        const label = document.querySelector('label[for="editar-upload-boleto"]');
        if (label && newInput.files.length > 0) {
          label.textContent = newInput.files[0].name;
        } else if (label) {
          label.textContent = 'Escolher arquivo';
        }
      });
      // Faz upload e extrai código de barras
      newInput.addEventListener('change', async function () {
        if (!newInput.files || newInput.files.length === 0) return;
        if (spinner) spinner.style.display = 'inline-block';
        const file = newInput.files[0];
        if (!file) {
          if (spinner) spinner.style.display = 'none';
          return;
        }
        const formData = new FormData();
        formData.append('arquivo_boleto', file); // sempre envia como 'arquivo_boleto'
        try {
          const response = await fetch('/api/extrair-codigo-barras', {
            method: 'POST',
            body: formData
          });
          if (spinner) spinner.style.display = 'none';
          if (!response.ok) throw new Error('Erro ao enviar arquivo para o servidor');
          const data = await response.json();
          if (data.codigo_barras) {
            editarCodigoBarrasInput.value = data.codigo_barras;
          } else {
            alert('Não foi possível extrair o código de barras.');
          }
        } catch (err) {
          console.error(err);
          if (spinner) spinner.style.display = 'none';
          alert('Erro no upload ou na extração do boleto.');
        }
      });
    }

    // Abre o modal existente
    $('#modalEditarConta').modal('show');
  }

  // Função para capturar corretamente o id da conta ao clicar no botão "Editar"
  function addEditEventListeners() {
    document.querySelectorAll('.btn-info').forEach(button => {
      button.addEventListener('click', function () {
        let contaId = this.getAttribute('data-id');
        let recorrenciaId = this.getAttribute('data-recorrencia-id');

        if (!contaId) {
          const parentRow = this.closest('tr');
          if (parentRow) {
            contaId = parentRow.getAttribute('data-conta-id');
            console.log('Obtido data-conta-id do TR:', contaId);
          }
        }
        console.log('Conta ID capturado:', contaId);
        console.log('Recorrência ID capturado:', recorrenciaId);

        if (!contaId || contaId === 'null' || !recorrenciaId) {
          console.error('Conta ID ou Recorrência ID não encontrado.');
          return;
        }

        // Agora inclui os dois parâmetros na URL
        fetch(`/get_conta/${contaId}/${recorrenciaId}`)
          .then(response => {
            if (!response.ok) {
              throw new Error('Conta ou recorrência não encontrada');
            }
            return response.json();
          })
          .then(conta => {
            openEditModal(conta);
          })
          .catch(error => console.error('Erro ao carregar dados da conta:', error));
      });
    });
  }

  // Função para salvar as alterações da conta
// Função para salvar as alterações da conta
// Função para salvar as alterações da conta
function addSaveEditEventListener() {
  const saveButton = document.getElementById('salvar-editar-conta');
  if (saveButton && !saveButton.dataset.listenerAdded) {
      saveButton.addEventListener('click', async function (e) {
          e.preventDefault();
          if (!confirm('Deseja salvar as alterações?')) return;

          const contaId = document.getElementById('editar-conta-id').value;
          const recorrenciaId = document.getElementById('editar-recorrencia-id').value;
          const valor = document.getElementById('editar-valor-conta').value;
          const dataVencimento = document.getElementById('editar-data-vencimento').value;
          const codigoBarras = document.getElementById('editar-codigo-barras').value;
          const observacoes = document.getElementById('editar-observacoes').value;
          const tipoConta = document.getElementById('editar-tipo_conta').value;
          const editarUploadBoletoInput = document.getElementById('editar-upload-boleto');

          const formData = new FormData();
          formData.append('valor', valor);
          formData.append('data_vencimento', dataVencimento);
          formData.append('codigo_barras', codigoBarras);
          formData.append('observacoes', observacoes);
          formData.append('tipo_conta', tipoConta);
          formData.append('recorrencia_id', recorrenciaId);

          if (editarUploadBoletoInput && editarUploadBoletoInput.files.length > 0) {
              formData.append('arquivo_boleto', editarUploadBoletoInput.files[0]);
          }

          showLoading(); // Mostrar indicador de carregamento

          try {
              const response = await fetch(`/update_conta/${contaId}`, {
                  method: 'PATCH',
                  body: formData
              });

              // Verificar se a resposta HTTP é OK (status 200-299)
              if (!response.ok) {
                  throw new Error(`Erro na resposta do servidor: ${response.status}`);
              }

              // Processar o JSON da resposta
              const result = await response.json();

              if (result.message) {
                  // Fechar o modal
                  $('#modalEditarConta').modal('hide');

                  // Atualizar apenas os dados da aba atual
                  const urlParams = new URLSearchParams(window.location.search);
                  const activeTab = urlParams.get('tab') || 'cadastrar-conta';

                  // Recarrega apenas os dados da aba atual com base no tipo
                  if (activeTab === 'cadastrar-conta') {
                      refreshContasList();
                  } else if (activeTab === 'recorrencia') {
                      refreshRecorrenciaList();
                  } else if (activeTab === 'vencimentos') {
                      refreshVencimentosCalendar();
                  } else if (activeTab === 'pagamentos') {
                      refreshPagamentosList();
                  }

                  // Mostrar feedback ao usuário
                  flashMessage('Conta atualizada com sucesso!', 'success');
              } else if (result.error) {
                  // Exibe a mensagem de erro específica retornada pelo servidor
                  alert('Erro ao atualizar a conta: ' + result.error);
              } else {
                  // Caso o resultado não tenha nem message nem error
                  alert('Erro desconhecido ao atualizar a conta.');
              }
          } catch (error) {
              console.error('Erro ao processar a requisição:', error);
              alert('Alteração realizada com sucesso.');
          } finally {
              hideLoading(); // Esconder indicador de carregamento, independente do resultado
          }
      });

      // Marca que o listener já foi adicionado para evitar duplicação
      saveButton.dataset.listenerAdded = "true";
  }
}


// Funções auxiliares para atualizar cada tipo de lista/visualização
function refreshContasList() {
  // Pegue as datas atuais do filtro de período
  const inputPeriodo = document.getElementById('filtro-periodo');
  if (inputPeriodo && inputPeriodo._flatpickr) {
      const instance = inputPeriodo._flatpickr;
      if (instance.selectedDates.length === 2) {
          const startDate = instance.selectedDates[0].toISOString().split('T')[0];
          const endDate = instance.selectedDates[1].toISOString().split('T')[0];
          fetchContasByRange(startDate, endDate);
      }
  }
}

function refreshRecorrenciaList() {
  // Recarregar os dados de recorrência
  // Se tiver um endpoint específico, chamar aqui
  const currentUrl = window.location.pathname;
  fetchPage('/contas/recorrentes', initializeRecorrenciaEvents);
}

function refreshVencimentosCalendar() {
  // Recarregar o calendário de vencimentos
  // Se tiver um endpoint específico, chamar aqui
  fetchPage('/contas_vencimentos', initializeContasVencimentosEvents);
}

function refreshPagamentosList() {
  // Recarregar a lista de pagamentos
  // Se tiver um endpoint específico, chamar aqui
  const inputPeriodo = document.getElementById('filtro-periodo-pg');
  if (inputPeriodo && inputPeriodo._flatpickr) {
      const instance = inputPeriodo._flatpickr;
      if (instance.selectedDates.length === 2) {
          const startDate = instance.selectedDates[0].toISOString().split('T')[0];
          const endDate = instance.selectedDates[1].toISOString().split('T')[0];
          fetchPagamentosByRange(startDate, endDate);
      }
  }
}


  // Evento para upload de boleto no modalEditarConta
  const editarUploadBoletoInput = document.getElementById('editar-upload-boleto');
  const editarCodigoBarrasInput = document.getElementById('editar-codigo-barras');
  const spinner = document.getElementById('loadingOverlay');

  if (editarUploadBoletoInput) {
    editarUploadBoletoInput.addEventListener('change', async function () {
      if (!editarUploadBoletoInput.files || editarUploadBoletoInput.files.length === 0) {
        return;
      }
      if (spinner) spinner.style.display = 'inline-block';
      const file = editarUploadBoletoInput.files[0];
      if (!file) {
        if (spinner) spinner.style.display = 'none';
        return;
      }
      const formData = new FormData();
      formData.append('arquivo_boleto', file);
      try {
        const response = await fetch('/api/extrair-codigo-barras', {
          method: 'POST',
          body: formData
        });
        if (spinner) spinner.style.display = 'none';
        if (!response.ok) {
          throw new Error('Erro ao enviar arquivo para o servidor');
        }
        const data = await response.json();
        if (data.codigo_barras) {
          editarCodigoBarrasInput.value = data.codigo_barras;
        } else {
          alert('Não foi possível extrair o código de barras.');
        }
      } catch (err) {
        console.error(err);
        if (spinner) spinner.style.display = 'none';
        alert('Erro no upload ou na extração do boleto.');
      }
    });
  }

  // Evento para upload de boleto no modalEditarConta (replicando o comportamento do cadastro principal)
  if (editarUploadBoletoInput && editarCodigoBarrasInput) {
    editarUploadBoletoInput.addEventListener('change', async function () {
      if (!editarUploadBoletoInput.files || editarUploadBoletoInput.files.length === 0) {
        return;
      }
      if (spinner) spinner.style.display = 'inline-block';
      const file = editarUploadBoletoInput.files[0];
      if (!file) {
        if (spinner) spinner.style.display = 'none';
        return;
      }
      const formData = new FormData();
      formData.append('arquivo_boleto', file);
      try {
        const response = await fetch('/api/extrair-codigo-barras', {
          method: 'POST',
          body: formData
        });
        if (spinner) spinner.style.display = 'none';
        if (!response.ok) {
          throw new Error('Erro ao enviar arquivo para o servidor');
        }
        const data = await response.json();
        if (data.codigo_barras) {
          editarCodigoBarrasInput.value = data.codigo_barras;
        } else {
          alert('Não foi possível extrair o código de barras.');
        }
      } catch (err) {
        console.error(err);
        if (spinner) spinner.style.display = 'none';
        alert('Erro no upload ou na extração do boleto.');
      }
    });
  }

  // Atualiza o label do input file ao selecionar arquivo (modal editar)
  if (editarUploadBoletoInput) {
    editarUploadBoletoInput.addEventListener('change', function () {
      const label = document.querySelector('label[for="editar-upload-boleto"]');
      if (label && editarUploadBoletoInput.files.length > 0) {
        label.textContent = editarUploadBoletoInput.files[0].name;
      } else if (label) {
        label.textContent = 'Escolher arquivo';
      }
    });
  }

  // Função para abrir o modal de edição com os dados de pagamento
  function openEditModalPagamento(conta, modoSubstituicao = false) {
    console.log('Debug em openEditModalPagamento:', conta, 'Modo substituição:', modoSubstituicao);

    // Preenche os campos do modal com os dados da conta
    document.getElementById('editar-conta-id').value = conta.id;
    document.getElementById('editar-recorrencia-id').value = conta.recorrencia_id;
    document.getElementById('editar-data-pagamento').value = conta.data_pagamentos;
    document.getElementById('editar-codigo-pagamento').value = conta.codigo_barras || '--';

    // Atualiza o título do modal baseado no modo
    const modalTitle = document.getElementById('modalEditarPagamentoLabel');
    if (modalTitle) {
      modalTitle.textContent = modoSubstituicao ? 'Substituir Comprovante de Pagamento' : 'Editar Pagamento';
    }

    // Mostra informações sobre comprovante existente se estiver em modo de substituição
    const comprovanteInfo = document.getElementById('comprovante-existente-info');
    if (modoSubstituicao && conta.comprovante_filename) {
      if (comprovanteInfo) {
        comprovanteInfo.style.display = 'block';
        comprovanteInfo.innerHTML = `
          <div class="alert alert-info">
            <i class="fa-solid fa-info-circle"></i>
            <strong>Comprovante atual:</strong>
            <a href="${conta.comprovante_filename}" target="_blank" class="btn btn-outline-primary btn-sm ml-2">
              <i class="fa-solid fa-file-pdf"></i> Ver comprovante atual
            </a>
            <br><small class="text-muted">Selecione um novo arquivo para substituir o comprovante existente.</small>
          </div>
        `;
      }
    } else if (comprovanteInfo) {
      comprovanteInfo.style.display = 'none';
    }

    if (conta.codigo_barras) {
      document.getElementById('info-pagamento').style.display = 'none';
    } else {
      document.getElementById('info-pagamento').style.display = 'flex';
    }

    document.getElementById('editar-banco-pagamento').value = conta.nome_banco || '--';
    document.getElementById('editar-codBanco-pagamento').value = conta.numero_banco || '--';
    document.getElementById('editar-agencia-pagamento').value = conta.agencia || '--';
    document.getElementById('editar-codAgencia-pagamento').value = conta.digito_agencia || '--';
    document.getElementById('editar-tipoConta-pagamento').value = conta.tipo_conta_bancaria || '--';
    document.getElementById('editar-conta-pagamento').value = conta.conta_corrente || '--';
    document.getElementById('editar-codConta-pagamento').value = conta.digito_conta || '--';
    document.getElementById('editar-documento-pagamento').value = conta.cnpj_cpf || '--';
    // Abre o modal usando o método do Bootstrap:
    $('#modalEditarPagamento').modal('show');
  }

  // Função para fechar o modal de pagamento
  function closeModalPagamento() {
    $('#modalEditarPagamento').modal('hide');
  }

  function initializeContasDateFilter() {
    const inputPeriodo = document.getElementById('filtro-periodo');
    if (!inputPeriodo) return;

    // Usa a função para pegar a semana atual (segunda a sábado)
    const [startOfWeek, endOfWeek] = getCurrentWeekRange();

    flatpickr(inputPeriodo, {
      mode: 'range',
      dateFormat: 'd/m/Y',
      locale: 'pt',
      defaultDate: [startOfWeek, endOfWeek], // Define a semana atual
      onReady: function (selectedDates, dateStr, instance) {
        // Assim que o Flatpickr estiver pronto, verifica se já há duas datas selecionadas
        if (instance.selectedDates.length === 2) {
          const startDate = instance.selectedDates[0].toISOString().split('T')[0];
          const endDate = instance.selectedDates[1].toISOString().split('T')[0];
          filtrarContasPorPeriodo(startDate, endDate);
        }
      },
      onClose: function (selectedDates) {
        if (selectedDates.length === 2) {
          const startDate = selectedDates[0].toISOString().split('T')[0];
          const endDate = selectedDates[1].toISOString().split('T')[0];
          filtrarContasPorPeriodo(startDate, endDate);
        }
      }
    });
  }

  function filtrarContasPorPeriodo(startDate, endDate) {
    const rows = document.querySelectorAll('#contas-list tr');

    // Monta objeto Date ignorando fuso
    const [anoInicio, mesInicio, diaInicio] = startDate.split('-');
    const dataInicio = new Date(anoInicio, mesInicio - 1, diaInicio);

    const [anoFim, mesFim, diaFim] = endDate.split('-');
    const dataFim = new Date(anoFim, mesFim - 1, diaFim);
    dataFim.setHours(23, 59, 59, 999); // inclui o fim do dia

    rows.forEach(row => {
      const tdData = row.querySelector('td:nth-child(5)');
      if (!tdData) return;

      // Se no <td> está "DD/MM/AAAA"
      const [dia, mes, ano] = tdData.textContent.split('/');
      const dataConta = new Date(ano, mes - 1, dia);

      // Comparação de datas
      if (dataConta >= dataInicio && dataConta <= dataFim) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }

  function initializePagamentoDateFilter() {
    const inputPeriodo = document.getElementById('filtro-periodo-pg');
    if (!inputPeriodo) return;

    // Usa a função para pegar a semana atual (segunda a sábado)
    const [startOfWeek, endOfWeek] = getCurrentWeekRange();

    flatpickr(inputPeriodo, {
      mode: 'range',
      dateFormat: 'd/m/Y',
      locale: 'pt',
      defaultDate: [startOfWeek, endOfWeek], // Define a semana atual
      onReady: function (selectedDates, dateStr, instance) {
        // Assim que o Flatpickr estiver pronto, verifica se já há duas datas selecionadas
        if (instance.selectedDates.length === 2) {
          const startDate = instance.selectedDates[0].toISOString().split('T')[0];
          const endDate = instance.selectedDates[1].toISOString().split('T')[0];
          filtrarPGPorPeriodo(startDate, endDate);
        }
      },
      onClose: function (selectedDates) {
        if (selectedDates.length === 2) {
          const startDate = selectedDates[0].toISOString().split('T')[0];
          const endDate = selectedDates[1].toISOString().split('T')[0];
          filtrarPGPorPeriodo(startDate, endDate);
        }
      }
    });
  }

  function filtrarPGPorPeriodo(startDate, endDate) {
    const rows = document.querySelectorAll('#contas-list tr');

    // Monta objeto Date ignorando fuso
    const [anoInicio, mesInicio, diaInicio] = startDate.split('-');
    const dataInicio = new Date(anoInicio, mesInicio - 1, diaInicio);

    const [anoFim, mesFim, diaFim] = endDate.split('-');
    const dataFim = new Date(anoFim, mesFim - 1, diaFim);
    dataFim.setHours(23, 59, 59, 999); // inclui o fim do dia

    rows.forEach(row => {
      const tdData = row.querySelector('td:nth-child(5)');
      if (!tdData) return;

      // Se no <td> está "DD/MM/AAAA"
      const [dia, mes, ano] = tdData.textContent.split('/');
      const dataConta = new Date(ano, mes - 1, dia);

      // Comparação de datas
      if (dataConta >= dataInicio && dataConta <= dataFim) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }

  // Adiciona ouvinte para botões com data-dismiss no modal
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('#modalEditarPagamento [data-dismiss="modal"]').forEach(button => {
      button.addEventListener('click', closeModalPagamento);
    });
  });

  // Nova função para tratar a edição de pagamento sem GET
  function addEditPaymentEventListeners() {
    document.querySelectorAll('#contas-list .btn-info').forEach(button => {
      button.addEventListener('click', function () {
        const contaId = this.getAttribute('data-id');
        const recorrenciaId = this.getAttribute('data-recorrencia-id');
        if (!contaId || !recorrenciaId) {
          console.error('Conta ID ou Recorrência ID não encontrado.');
          return;
        }
        // Obtém os dados reais da conta antes de chamar o modal de pagamento.
        fetch(`/get_conta/${contaId}/${recorrenciaId}`)
          .then(response => {
            if (!response.ok) throw new Error('Conta ou recorrência não encontrada');
            return response.json();
          })
          .then(conta => {
            openEditModalPagamento(conta);
          })
          .catch(error => console.error('Erro ao carregar dados da conta para pagamento:', error));
      });
    });
  }

  // Função para tratar eventos de substituição de comprovante
  function addSubstituirComprovanteEventListeners() {
    document.querySelectorAll('.btn-substituir-comprovante').forEach(button => {
      button.addEventListener('click', function () {
        const contaId = this.getAttribute('data-id');
        const recorrenciaId = this.getAttribute('data-recorrencia-id');
        if (!contaId || !recorrenciaId) {
          console.error('Conta ID ou Recorrência ID não encontrado.');
          return;
        }

        // Obtém os dados da conta e abre o modal em modo de substituição
        fetch(`/get_conta/${contaId}/${recorrenciaId}`)
          .then(response => {
            if (!response.ok) throw new Error('Conta ou recorrência não encontrada');
            return response.json();
          })
          .then(conta => {
            openEditModalPagamento(conta, true); // true indica modo de substituição
          })
          .catch(error => console.error('Erro ao carregar dados da conta para substituição:', error));
      });
    });
  }

  // Função global para buscar pagamentos por período
  function fetchPagamentosByRange(startDate, endDate) {
    console.log('Buscando pagamentos para o período:', startDate, 'até', endDate);
    return fetch(`/get_pagamentos_por_mes?start=${startDate}&end=${endDate}`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Erro ao buscar pagamentos por período');
        }
        return response.json();
      })
      .then(contas => {
        // Atualiza a tabela com os novos dados
        updatePagamentosTable(contas);
        return contas;
      })
      .catch(error => {
        console.error('Erro ao carregar contas:', error);
        throw error;
      });
  }

  // Função para salvar as conciliações do pagamento
  function addSaveEditPagamentoEventListener() {
    const saveButton = document.getElementById('salvar-editar-pagamento');

    if (saveButton) {
      saveButton.addEventListener('click', async function () {
        try {
          // Captura os elementos do formulário
          const contaId = document.getElementById('editar-conta-id')?.value;
          const recorrenciaId = document.getElementById('editar-recorrencia-id')?.value;
          const dataPagamento = document.getElementById('editar-data-pagamento')?.value;
          const fileInput = document.getElementById('editar-comprovante-pagamento');

          // Verifica se os campos necessários existem
          if (!recorrenciaId || !dataPagamento) {
            alert("Erro: Campos obrigatórios não foram preenchidos.");
            return;
          }

          // Verifica se está em modo de substituição
          const modalTitle = document.getElementById('modalEditarPagamentoLabel');
          const isSubstituicao = modalTitle && modalTitle.textContent.includes('Substituir');

          const confirmMessage = isSubstituicao ?
            'Deseja substituir o comprovante de pagamento?' :
            'Deseja salvar as alterações?';

          if (!confirm(confirmMessage)) {
            return;
          }

          // Obtém o arquivo
          const file = fileInput?.files?.[0];

          // Validação específica para modo de substituição
          if (isSubstituicao && !file) {
            alert('Por favor, selecione um arquivo para substituir o comprovante existente.');
            return;
          }

          // Criando objeto FormData para enviar tanto os dados quanto o arquivo
          let formData = new FormData();
          formData.append('data_pagamentos', dataPagamento);
          formData.append('recorrencia_id', recorrenciaId);

          if (file) {
            formData.append('file', file);
          }

          // Mostra loading
          showLoading();

          // Enviar dados para o backend
          const response = await fetch(`/update_pagamento/${recorrenciaId}`, {
            method: 'PATCH',
            body: formData
          });

          const result = await response.json();

          if (result.success) {
            // Fecha o modal
            $('#modalEditarPagamento').modal('hide');

            // Recupera as datas do filtro atual
            const inputPeriodo = document.getElementById('filtro-periodo-pg');
            const instance = inputPeriodo._flatpickr;

            if (instance && instance.selectedDates.length === 2) {
              const startDate = instance.selectedDates[0].toISOString().split('T')[0];
              const endDate = instance.selectedDates[1].toISOString().split('T')[0];

              // Atualiza a tabela com os dados do período atual
              await fetchPagamentosByRange(startDate, endDate);
            }

            // Mostra mensagem de sucesso específica
            const successMessage = isSubstituicao ?
              'Comprovante substituído com sucesso!' :
              'Pagamento atualizado com sucesso!';
            flashMessage(successMessage, 'success');
          } else {
            throw new Error(result.error || 'Erro ao atualizar o pagamento');
          }
        } catch (error) {
          console.error('Erro ao atualizar o pagamento:', error);
          flashMessage(error.message || 'Erro ao atualizar o pagamento.', 'danger');
        } finally {
          hideLoading();
        }
      });

      const fileInput = document.getElementById('editar-comprovante-pagamento');
      if (fileInput) {
        fileInput.addEventListener('change', function () {
          const fileName = this.files[0]?.name || 'Nenhum arquivo selecionado';
          this.nextElementSibling.innerText = fileName;
        });
      }
    } else {
      console.error("Erro: Botão de salvar não encontrado!");
    }
  }

  // Função para formatar data para o padrão brasileiro
  function formatarDataBR(data) {
    const date = new Date(data);
    const dia = String(date.getDate()).padStart(2, '0');
    const mes = String(date.getMonth() + 1).padStart(2, '0');
    const ano = date.getFullYear();
    return `${dia}/${mes}/${ano}`;
  }

  // Função para formatar valor para o padrão BRL
  function formatarValorBRL(valor) {
    return valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  // Evento de input para formatar valor conforme digitado
  const valorContaInput = document.getElementById('valor-conta');
  if (valorContaInput) {
    valorContaInput.addEventListener('input', function (e) {
      let valor = e.target.value.replace(/\D/g, '');
      valor = (valor / 100).toFixed(2);
      e.target.value = formatarValorBRL(Number(valor));
    });
  }

  // ==================================
  // 5. Inicialização de eventos por aba
  // ==================================

  // Fornecedores
  function initializeFornecedoresEvents() {
    loadFornecedores(); // Carrega a tabela ao selecionar a aba
    initializeFornecedoresForm();
    addSaveEditFornEventListener();
  }

  // Funcionários
  function initializeFuncionariosEvents() {
    loadFuncionarios();
    initializeFuncionariosForm();

    // Adiciona eventos aos botões de editar funcionários
    document.addEventListener('DOMContentLoaded', function () {
      document.querySelectorAll('#modalEditarFuncionario [data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', closeModalFuncionario);
      });
    });
  }

  // Empresas
  function initializeEmpresasEvents() {
    loadEmpresas(); // Carrega a tabela na aba de empresas
    initializeEmpresasForm();
  }

  // Recorrências
  function initializeRecorrenciaEvents() {
  }

  function initializeFornecedoresForm() {
    const formFornecedores = document.getElementById('form-fornecedores');
    const btnCadastrar = document.getElementById('botaoCadastrarFornecedor');

    if (formFornecedores && btnCadastrar) {
      btnCadastrar.addEventListener('click', function (event) {
        event.preventDefault(); // evita qualquer envio padrão

        if (confirm('Confirmar o cadastro deste fornecedor?')) {
          const formData = new FormData(formFornecedores);

          fetch('/submit_fornecedor', {
            method: 'POST',
            body: formData
          })
            .then(response => response.json())
            .then(data => {
              if (data.status === 'success') {
                alert(data.message);
                location.reload();
              } else {
                alert(`Erro: ${data.message}`);
              }
            })
            .catch(error => {
              console.error('Erro ao cadastrar fornecedor:', error);
              alert('Erro ao cadastrar fornecedor.');
            });
        }
      });
    } else {
      console.error("Formulário ou botão de cadastrar fornecedor não encontrado no DOM.");
    }

    addEditFornEventListeners();
    addSaveEditFornEventListener();
  }

  function initializeFuncionariosForm() {
    const formFuncionarios = document.getElementById('form-funcionario');
    const btnCadastrar = document.getElementById('botaoCadastrarFuncionario');

    if (formFuncionarios && btnCadastrar) {
      btnCadastrar.addEventListener('click', function (event) {
        event.preventDefault(); // evita qualquer envio padrão

        if (confirm('Confirmar o cadastro deste funcionário?')) {
          const formData = new FormData(formFuncionarios);

          fetch('/submit_funcionarios', {
            method: 'POST',
            body: formData
          })
            .then(response => response.json())
            .then(data => {
              if (data.status === 'success') {
                alert(data.message);
                location.reload();
              } else {
                alert(`Erro: ${data.message}`);
              }
            })
            .catch(error => {
              console.error('Erro ao cadastrar funcionário:', error);
              alert('Erro ao cadastrar funcionário.');
            });
        }
      });
    } else {
      console.error("Formulário ou botão de cadastrar funcionário não encontrado no DOM.");
    }

    addEditFornEventListeners();
    addSaveEditFornEventListener();
  }

  function initializeEmpresasForm() {
    const formEmpresas = document.getElementById('form-empresas');
    const btnCadastrar = document.getElementById('botaoCadastrarEmpresa');

    if (formEmpresas && btnCadastrar) {
      btnCadastrar.addEventListener('click', function (event) {
        event.preventDefault(); // evita envio padrão

        if (confirm('Confirmar o cadastro desta empresa?')) {
          const formData = new FormData(formEmpresas);

          fetch('/submit_empresa', {
            method: 'POST',
            body: formData
          })
            .then(response => response.json())
            .then(data => {
              if (data.status === 'success') {
                alert(data.message);
                location.reload(); // ou outro comportamento se desejar
              } else {
                alert(`Erro: ${data.message}`);
              }
            })
            .catch(error => {
              console.error('Erro ao cadastrar empresa:', error);
              alert('Erro ao cadastrar empresa.');
            });
        }
      });
    } else {
      console.error("Formulário ou botão de cadastrar empresa não encontrado no DOM.");
    }
  }

  function updatePagamentosTable(contas) {
    // Ordena as contas pela data de vencimento (mais antiga para mais nova)
    contas.sort((a, b) => {
      const [diaA, mesA, anoA] = a.data_vencimento.split('/');
      const [diaB, mesB, anoB] = b.data_vencimento.split('/');
      const dateA = new Date(anoA, mesA - 1, diaA);
      const dateB = new Date(anoB, mesB - 1, diaB);
      return dateA - dateB;
    });

    // Atualiza o dropdown de Empresa Pagadora baseado nas contas recebidas
    const filtroEmpresa = document.getElementById('filtro-empresa-pg');
    if (filtroEmpresa) {
      const uniqueEmpresas = [...new Set(contas.map(c => c.empresa_pagadora))].filter(Boolean);
      let optionsHtml = '<option value="">Todas Empresas</option>';
      uniqueEmpresas.forEach(emp => {
        optionsHtml += `<option value="${emp}">${emp}</option>`;
      });
      // Preserva o valor atual se existir
      const currentValue = filtroEmpresa.value;
      filtroEmpresa.innerHTML = optionsHtml;
      if (uniqueEmpresas.includes(currentValue)) {
        filtroEmpresa.value = currentValue;
      }
      // Filtra os dados pelo valor selecionado
      const selectedEmp = filtroEmpresa.value;
      if (selectedEmp) {
        contas = contas.filter(c => c.empresa_pagadora === selectedEmp);
      }
    }

    // Novo Filtro por Fornecedor
    const filtroFornecedor = document.getElementById('filtro-fornecedor-pg');
    if (filtroFornecedor) {
      const uniqueFornecedores = [...new Set(contas.map(c => c.fornecedor))].filter(Boolean);
      let optionsHtml = '<option value="">Todos Fornecedores</option>';
      uniqueFornecedores.forEach(fornec => {
        optionsHtml += `<option value="${fornec}">${fornec}</option>`;
      });
      const currentFornecedor = filtroFornecedor.value;
      filtroFornecedor.innerHTML = optionsHtml;
      if (uniqueFornecedores.includes(currentFornecedor)) {
        filtroFornecedor.value = currentFornecedor;
      }
      const selectedFornec = filtroFornecedor.value;
      if (selectedFornec) {
        contas = contas.filter(c => c.fornecedor === selectedFornec);
      }
    }

    // Calcula os totais
    const totalAPagar = contas
      .filter(conta => !conta.data_pagamento)
      .reduce((total, conta) => total + Number(conta.valor || 0), 0);

    const totalPago = contas
      .filter(conta => conta.data_pagamento)
      .reduce((total, conta) => total + Number(conta.valor || 0), 0);

    // Atualiza os elementos de total
    const totalAPagarElement = document.getElementById('total-a-pagar');
    const totalPagoElement = document.getElementById('total-pago');

    if (totalAPagarElement) {
      totalAPagarElement.textContent = formatarValor(totalAPagar);
    }

    if (totalPagoElement) {
      totalPagoElement.textContent = formatarValor(totalPago);
    }

    const tabela = document.getElementById('contas-list');
    tabela.innerHTML = '';

    if (contas.length === 0) {
      tabela.innerHTML = '<tr><td colspan="10">Nenhuma conta cadastrada.</td></tr>';
      return;
    }

    contas.forEach(c => {

      const beneficiario = c.fornecedor ? c.fornecedor : (c.funcionario || '-');

      const linha = document.createElement('tr');
      linha.setAttribute('data-conta-id', c.id);
      linha.innerHTML = `
      <td>${c.nome_conta}</td>
      <td>${renderTexto(c.empresa_pagadora)}</td>
      <td>${renderTexto(beneficiario)}</td>
      <td>${formatarValor(c.valor)}</td>
      <td>${renderData(c.data_vencimento)}</td>
      <td>${renderData(c.data_pagamento)}</td>
      <td>${renderComprovanteLink(c.comprovante_filename, c.id, c.recorrencia_id)}</td>
      <td style="padding: 5px; text-align: center;">
        <button class="btn btn-info btn-sm" data-id="${c.id}" data-recorrencia-id="${c.recorrencia_id}">
          <i class="fa-solid fa-arrow-right-arrow-left"></i>
        </button>
      </td>
    `;
      tabela.appendChild(linha);
    });

    addEditPaymentEventListeners();
    addSubstituirComprovanteEventListeners();
    addDeleteEventListeners();
    atualizarSomaValorPagamentos();
  }

  // Pagamentos
  function initializePagamentosEvents() {
    // Variável para armazenar os dados
    let contasData = [];

    // 1) Função que faz o fetch no servidor com intervalo de datas
    function fetchPagamentosByRange(startDate, endDate) {
      console.log('Buscando pagamentos para o período:', startDate, 'até', endDate);
      fetch(`/get_pagamentos_por_mes?start=${startDate}&end=${endDate}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Erro ao buscar pagamentos por período');
          }
          return response.json();
        })
        .then(contas => {
          // Armazena as contas retornadas
          contasData = contas;
          // Atualiza a tabela (render)
          updatePagamentosTable(contasData);
        })
        .catch(error => {
          console.error('Erro ao carregar contas:', error);
        });
    }

    // 2) Inicializa o Flatpickr para datas de pagamento
    function initializePagamentoDateFilter() {
      const inputPeriodo = document.getElementById('filtro-periodo-pg');
      if (!inputPeriodo) return;

      // Usa sua função para pegar a semana atual [segunda, sábado]
      const [startOfWeek, endOfWeek] = getCurrentWeekRange();

      flatpickr(inputPeriodo, {
        mode: 'range',
        dateFormat: 'd/m/Y',
        locale: 'pt',
        defaultDate: [startOfWeek, endOfWeek], // Define a semana atual
        onReady: function (selectedDates, dateStr, instance) {
          // Se já vier com duas datas, dispare o fetch inicial
          if (instance.selectedDates.length === 2) {
            const startDate = instance.selectedDates[0].toISOString().split('T')[0];
            const endDate = instance.selectedDates[1].toISOString().split('T')[0];
            fetchPagamentosByRange(startDate, endDate);
          }
        },
        onClose: function (selectedDates) {
          if (selectedDates.length === 2) {
            const startDate = selectedDates[0].toISOString().split('T')[0];
            const endDate = selectedDates[1].toISOString().split('T')[0];
            fetchPagamentosByRange(startDate, endDate);
          }
        }
      });
    }

    // 3) Chamamos a inicialização do filtro de datas
    initializePagamentoDateFilter();

    // 4) Filtros adicionais (empresa, fornecedor) que você ainda quer fazer no front
    const filtroEmpresa = document.getElementById('filtro-empresa-pg');
    const filtroClassificacao = document.getElementById('filtro-classificacao-pg');
    // ^ se quiser usar ou não
    const filtroFornecedor = document.getElementById('filtro-fornecedor-pg');

    if (filtroEmpresa) {
      filtroEmpresa.addEventListener('change', function () {
        // Filtra localmente (empresa) dentro de contasData
        updatePagamentosTable(contasData);
      });
    }

    // Se usar classificação no front:
    if (filtroClassificacao) {
      filtroClassificacao.addEventListener('change', function () {
        // Também filtra localmente
        updatePagamentosTable(contasData);
      });
    }

    if (filtroFornecedor) {
      filtroFornecedor.addEventListener('change', function () {
        updatePagamentosTable(contasData);
      });
    }

    // 5) Inicializa o listener para salvar/editar pagamentos
    addSaveEditPagamentoEventListener();
  }

  // Cadastro (Contas)
  function initializeCadastroEvents() {
    // 1) Carrega SELECTs
    loadEmpresasForDropdown();
    loadFornecedoresForDropdown();
    loadFuncionarios();

    // 2) Declara uma variável global dentro da função para armazenar as contas
    let contasData = [];

    // 3) Função para buscar contas no servidor dado um intervalo
    function fetchContasByRange(startDate, endDate) {
      fetch(`/get_contas_por_mes?start=${startDate}&end=${endDate}`)
        .then(response => {
          if (!response.ok) {
            throw new Error('Erro ao buscar contas');
          }
          return response.json();
        })
        .then(contas => {
          // Atualiza nosso array local e re-renderiza a tabela
          contasData = contas;
          updateContasTable(contasData);
          const total = contas.reduce((acc, c) => acc + (parseFloat(c.valor) || 0), 0);
          document.getElementById("total-valor").textContent = total.toLocaleString("pt-BR", {
            style: "currency",
            currency: "BRL"
          });
        })
        .catch(error => {
          console.error('Erro ao carregar contas:', error);
        });
    }

    const tipoBenefSelect = document.getElementById('tipoBeneficiario');
    const containerFornecedor = document.getElementById('containerFornecedor');
    const containerFuncionario = document.getElementById('containerFuncionario');
    const fornecedorSelect = document.getElementById('fornecedorSelect');
    const funcionarioSelect = document.getElementById('funcionarioSelect');

    // 3) Lógica para exibir/ocultar os <select> de acordo com "fornecedor" ou "funcionario"
    if (tipoBenefSelect) {
      tipoBenefSelect.addEventListener('change', function () {
        const valor = this.value;
        if (valor === 'fornecedor') {
          containerFornecedor.style.display = 'block';
          containerFuncionario.style.display = 'none';
        } else if (valor === 'funcionario') {
          containerFornecedor.style.display = 'none';
          containerFuncionario.style.display = 'block';
        } else {
          containerFornecedor.style.display = 'none';
          containerFuncionario.style.display = 'none';
        }
      });
    }

    fetch('/get_fornecedores')
      .then(resp => resp.json())
      .then(data => {
        data.forEach(f => {
          const option = document.createElement('option');
          option.value = f.id;
          option.textContent = f.nome;
          fornecedorSelect.appendChild(option);
        });
      })
      .catch(err => console.error('Erro ao carregar fornecedores:', err));

    // 5) Carrega a lista de funcionários e preenche o <select> #funcionarioSelect
    fetch('/get_funcionarios')
      .then(resp => resp.json())
      .then(data => {
        data.forEach(func => {
          const option = document.createElement('option');
          option.value = func.id;
          option.textContent = func.nome;
          funcionarioSelect.appendChild(option);
        });
      })
      .catch(err => console.error('Erro ao carregar funcionários:', err));


    // 4) Inicializa o Flatpickr e faz a cada mudança de datas um novo fetch
    function initializeContasDateFilter() {
      const inputPeriodo = document.getElementById('filtro-periodo');
      if (!inputPeriodo) return;

      // Sua função que devolve [segundaDaSemana, sabadoDaSemana]
      const [startOfWeek, endOfWeek] = getCurrentWeekRange();

      flatpickr(inputPeriodo, {
        mode: 'range',
        dateFormat: 'd/m/Y',
        locale: 'pt',
        defaultDate: [startOfWeek, endOfWeek],
        onReady: function (selectedDates, dateStr, instance) {
          // Se já vier com duas datas selecionadas (por ex. defaultDate),
          // a gente já dispara o fetch automaticamente
          if (instance.selectedDates.length === 2) {
            const startDate = instance.selectedDates[0].toISOString().split('T')[0];
            const endDate = instance.selectedDates[1].toISOString().split('T')[0];
            fetchContasByRange(startDate, endDate);
          }
        },
        onClose: function (selectedDates) {
          // Toda vez que o usuário fechar o calendário com duas datas,
          // chamamos o fetch novamente
          if (selectedDates.length === 2) {
            const startDate = selectedDates[0].toISOString().split('T')[0];
            const endDate = selectedDates[1].toISOString().split('T')[0];
            fetchContasByRange(startDate, endDate);
          }
        }
      });
    }

    // 5) Chama a inicialização do Flatpickr (que por sua vez chama fetchContasByRange)
    initializeContasDateFilter();

    // 6) Filtros adicionais (empresa, fornecedor etc.) – continuam
    const filtroEmpresa = document.getElementById('filtro-empresa');
    const filtroClassificacao = document.getElementById('filtro-classificacao');
    const filtroFornecedor = document.getElementById('filtro-fornecedor');

    if (filtroEmpresa) {
      filtroEmpresa.addEventListener('change', function () {
        // Como o array contasData já está filtrado pela data no backend,
        // aqui só filtramos localmente (empresa/fornecedor) ou, se preferir,
        // refaça outro fetch com ?empresa=...
        updateContasTable(contasData);
      });
    }

    if (filtroClassificacao) {
      filtroClassificacao.addEventListener('change', function () {
        updateContasTable(contasData);
      });
    }

    if (filtroFornecedor) {
      filtroFornecedor.addEventListener('change', function () {
        updateContasTable(contasData);
      });
    }

    // 7) Formulário de cadastro
    const formContas = document.getElementById('form-contas');
    if (formContas) {
      formContas.addEventListener('submit', function (event) {
        event.preventDefault(); // Impede o submit normal do formulário

        // Exibe a janela de confirmação
        if (!confirm('Deseja realmente cadastrar esta conta?')) {
          return; // Se o usuário clicar em "Cancelar", interrompemos aqui
        }

        // Se chegar até aqui, o usuário confirmou
        const formData = new FormData(formContas);
        console.log('Debug - Parâmetros do Form:', Object.fromEntries(formData.entries()));
        const dataVencimento = formData.get('data_vencimento');
        formData.set('data_vencimento', formatDateForSQL(dataVencimento));

        fetch('/submit_conta', {
          method: 'POST',
          body: formData
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Erro de rede ou servidor');
            }
            return response.json();
          })
          .then(result => {
            if (result.status === 'success') {
              flashMessage(result.message, 'success');
              formContas.reset();

              // (Opcional) Atualiza a tabela no período atual do Flatpickr
              const inputPeriodo = document.getElementById('filtro-periodo');
              const instance = inputPeriodo && inputPeriodo._flatpickr;
              if (instance && instance.selectedDates.length === 2) {
                const startDate = instance.selectedDates[0].toISOString().split('T')[0];
                const endDate = instance.selectedDates[1].toISOString().split('T')[0];
                fetchContasByRange(startDate, endDate);
              }
            } else {
              flashMessage(result.message, 'danger');
            }
          })
          .catch(err => {
            console.error('Erro no fetch:', err);
            flashMessage('Erro inesperado ao cadastrar conta.', 'danger');
          });
      });
    }

    // 8) Resto (upload, eventos de excluir, editar etc.)
    // ...
    document.querySelectorAll('input[name="recorrencia"]').forEach((elem) => {
      elem.addEventListener('change', () => {
        const container = document.getElementById('recorrencia-quantidade-container');
        container.style.display = (elem.value === 'unica') ? 'none' : 'block';
      });
    });

    // Exemplo do overlay e extração de código de barras
    const uploadBoletoInput = document.getElementById('upload-boleto');
    const codigoBarrasInput = document.getElementById('codigo-barras');
    const spinner = document.getElementById('loadingOverlay');

    if (uploadBoletoInput) {
      uploadBoletoInput.addEventListener('change', async function () {
        if (!uploadBoletoInput.files || uploadBoletoInput.files.length === 0) {
          return;
        }
        spinner.style.display = 'inline-block'; // mostra spinner

        const file = uploadBoletoInput.files[0];
        if (!file) {
          spinner.style.display = 'none';
          return;
        }

        const formData = new FormData();
        formData.append('arquivo_boleto', file);

        try {
          const response = await fetch('/api/extrair-codigo-barras', {
            method: 'POST',
            body: formData
          });

          spinner.style.display = 'none';

          if (!response.ok) {
            throw new Error('Erro ao enviar arquivo para o servidor');
          }

          const data = await response.json();
          if (data.codigo_barras) {
            codigoBarrasInput.value = data.codigo_barras;
          } else {
            alert('Não foi possível extrair o código de barras.');
          }
        } catch (err) {
          console.error(err);
          spinner.style.display = 'none';
          alert('Erro no upload ou na extração do boleto.');
        }
      });
    }

    // Atualiza o label do input file ao selecionar arquivo (cadastro)
    if (uploadBoletoInput) {
      uploadBoletoInput.addEventListener('change', function () {
        const label = document.querySelector('label[for="upload-boleto"]');
        if (label && uploadBoletoInput.files.length > 0) {
          label.textContent = uploadBoletoInput.files[0].name;
        } else if (label) {
          label.textContent = 'Escolher arquivo';
        }
      });
    }

    addDeleteEventListeners();
    addEditEventListeners();
    addSaveEditEventListener();
  }

  // Dados Bancários
  function initializeDadosBancariosEvents() {
    loadDadosBancarios();
    loadFornecedoresForDropdown();
    loadFuncionarios();

    // Adiciona eventos aos botões de editar dados bancários
    document.addEventListener('DOMContentLoaded', function () {
      document.querySelectorAll('#modalEditarDadosBancarios [data-dismiss="modal"]').forEach(button => {
        button.addEventListener('click', closeModalDadosBancarios);
      });
    });

    // 2) Referência para os elementos do formulário
    const tipoBenefSelect = document.getElementById('tipoBeneficiario');
    const containerFornecedor = document.getElementById('containerFornecedor');
    const containerFuncionario = document.getElementById('containerFuncionario');
    const fornecedorSelect = document.getElementById('fornecedorSelect');
    const funcionarioSelect = document.getElementById('funcionarioSelect');

    // 3) Lógica para exibir/ocultar os <select> de acordo com "fornecedor" ou "funcionario"
    if (tipoBenefSelect) {
      tipoBenefSelect.addEventListener('change', function () {
        const valor = this.value;
        if (valor === 'fornecedor') {
          containerFornecedor.style.display = 'block';
          containerFuncionario.style.display = 'none';
        } else if (valor === 'funcionario') {
          containerFornecedor.style.display = 'none';
          containerFuncionario.style.display = 'block';
        } else {
          containerFornecedor.style.display = 'none';
          containerFuncionario.style.display = 'none';
        }
      });
    }

    // 4) Carrega a lista de fornecedores e preenche o <select> #fornecedorSelect
    fetch('/get_fornecedores')
      .then(resp => resp.json())
      .then(data => {
        data.forEach(f => {
          const option = document.createElement('option');
          option.value = f.id;
          option.textContent = f.nome;
          fornecedorSelect.appendChild(option);
        });
      })
      .catch(err => console.error('Erro ao carregar fornecedores:', err));

    // 5) Carrega a lista de funcionários e preenche o <select> #funcionarioSelect
    fetch('/get_funcionarios')
      .then(resp => resp.json())
      .then(data => {
        data.forEach(func => {
          const option = document.createElement('option');
          option.value = func.id;
          option.textContent = func.nome;
          funcionarioSelect.appendChild(option);
        });
      })
      .catch(err => console.error('Erro ao carregar funcionários:', err));

    // 6) Inicializa a lógica do formulário (evento de clique em "Salvar")
    initializeDadosBancariosForm();
  }

  // Função específica para tratar o formulário de Dados Bancários via Fetch
  // Modificação para o arquivo contas_home.js

function initializeDadosBancariosForm() {
  // Seleciona o formulário
  const formDados = document.getElementById('form-dados-bancarios');
  // Seleciona o botão de salvar
  const btnSalvar = document.getElementById('botaoSalvarDadosBancarios');

  if (formDados && btnSalvar) {
      // Intercepta o clique no botão "Salvar"
      btnSalvar.addEventListener('click', function (event) {
          event.preventDefault(); // evita enviar o form pelo método padrão

          if (confirm('Confirmar o cadastro dos dados bancários?')) {
              // Monta o FormData com todos os campos do formulário
              const formData = new FormData(formDados);

              // Faz a requisição AJAX em vez do submit tradicional
              fetch('/submit_dadosbancarios', {
                  method: 'POST',
                  body: formData
              })
              .then(response => response.json())
              .then(data => {
                  if (data.status === 'success') {
                      // Em vez de location.reload(), apenas mostra um alerta
                      alert(data.message);

                      // Limpa o formulário
                      formDados.reset();

                      // Recarrega a listagem de dados bancários se necessário
                      loadDadosBancarios();

                      // Opcionalmente, mude para a aba de listagem
                      document.getElementById('tab-listar-dados').click();
                  } else {
                      alert(`Erro: ${data.message}`);
                  }
              })
              .catch(error => {
                  console.error('Erro ao cadastrar dados bancários:', error);
                  alert('Erro ao cadastrar dados bancários.');
              });
          }
      });
  } else {
      console.error("Formulário (#form-dados-bancarios) ou botão (#botaoSalvarDadosBancarios) não encontrado no DOM.");
  }
}


  // Vencimentos
  function initializeContasVencimentosEvents() {
    const calendarEl = document.getElementById('calendar');

    if (calendarEl) {
      // 1) Fazer fetch em '/get_vencimentos'
      fetch('/get_vencimentos')
        .then(response => {
          if (!response.ok) {
            throw new Error(`Erro ao buscar vencimentos: ${response.statusText}`);
          }
          return response.json();
        })
        .then(eventos => {

          // 2) Inicializa o FullCalendar com esses eventos
          const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'pt-br',
            headerToolbar: {
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            events: eventos
          });

          calendar.render();
        })
        .catch(err => console.error('Erro ao carregar vencimentos:', err));
    }
  }

  // Atualiza a tabela de contas e adiciona eventos de clique aos botões "Excluir" e "Editar"
  // Atualiza a tabela de contas e adiciona eventos de clique aos botões "Excluir" e "Editar"
  // Atualiza a tabela de contas e adiciona eventos de clique aos botões "Excluir" e "Editar"
function updateContasTable(contas) {
  // Ordena as contas pela data de vencimento (mais antiga para mais nova)
  contas.sort((a, b) => {
    const [diaA, mesA, anoA] = a.data_vencimento.split('/');
    const [diaB, mesB, anoB] = b.data_vencimento.split('/');
    const dateA = new Date(anoA, mesA - 1, diaA);
    const dateB = new Date(anoB, mesB - 1, diaB);
    return dateA - dateB;
  });

  const tabela = document.getElementById('contas-list');
  tabela.innerHTML = '';

  if (contas.length === 0) {
    tabela.innerHTML = '<tr><td colspan="11">Nenhuma conta cadastrada.</td></tr>';
    return;
  }

  contas.forEach(c => {
    // Verificar no console os dados recebidos para debug
    console.log('Dados da conta:', c);

    const beneficiario = c.fornecedor ? c.fornecedor : (c.funcionario || '-');

    // Verificar qual campo contém a classificação (tipo_conta ou classificacao)
    const classificacao = c.tipo_conta || c.classificacao || '-';

    const linha = document.createElement('tr');
    linha.setAttribute('data-conta-id', c.id);
    linha.innerHTML = `
      <td>${c.nome_conta || '-'}</td>
      <td>${renderTexto(c.empresa_pagadora) || '-'}</td>
      <td>${renderTexto(beneficiario)}</td>
      <td>${formatarValor(c.valor)}</td>
      <td>${renderData(c.data_vencimento)}</td>
      <td>${renderCodigoBarras(c.codigo_barras) || '-'}</td>
      <td>${c.recorrencia || 'Mensal'}</td>
      <td>${classificacao}</td>
      <td>${renderTexto(c.observacoes) || '-'}</td>
      <td>${renderBoletoLink(c.boleto_filename)}</td>
      <td style="padding: 5px; text-align: center;">
        <button class="btn btn-info btn-sm" data-id="${c.id}" data-recorrencia-id="${c.recorrencia_id || ''}">
          <i class="fa-solid fa-pen-to-square"></i>
        </button>
        <button class="btn btn-danger btn-sm delete-conta" data-id="${c.id}">
          <i class="fa-solid fa-trash"></i>
        </button>
      </td>
    `;
    tabela.appendChild(linha);
  });

  // Adiciona eventos aos botões de editar e excluir
  addEditEventListeners();
  addDeleteEventListeners();

  // Atualiza o valor total
  atualizarSomaValor();
}

// Função para renderizar o link do boleto
function renderBoletoLink(filename) {
  if (!filename) return '--';
  return `<a href="${filename}" target="_blank" class="btn btn-outline-primary btn-sm">
            <i class="fa-solid fa-file-pdf"></i> Ver
          </a>`;
}

// Função para renderizar comprovante com opção de substituição
function renderComprovanteLink(filename, contaId, recorrenciaId) {
  if (!filename) return '--';
  return `
    <div class="d-flex align-items-center gap-2">
      <a href="${filename}" target="_blank" class="btn btn-outline-primary btn-sm">
        <i class="fa-solid fa-file-pdf"></i> Ver
      </a>
      <button class="btn btn-outline-warning btn-sm btn-substituir-comprovante"
              data-id="${contaId}"
              data-recorrencia-id="${recorrenciaId}"
              title="Substituir comprovante">
        <i class="fa-solid fa-arrow-rotate-right"></i>
      </button>
    </div>
  `;
}

// Função para renderizar o código de barras com tooltip e ícone de cópia
function renderCodigoBarras(texto) {
  if (!texto) return '--';
  if (texto.length <= 12) return texto;
  const curto = texto.substring(0, 12);
  return `<span title="${texto}">${curto}...</span> <span class="copy-icon" style="cursor:pointer;" onclick="copyCodigoBarras('${texto}')"><i class="fa-solid fa-copy"></i></span>`;
}

  // Função para exibir detalhes do fornecedor
  function showDetails(fornecedorId) {
    fetch(`/get_fornecedor/${fornecedorId}`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Erro ao buscar detalhes do fornecedor');
        }
        return response.json();
      })
      .then(fornecedor => {
        // Exibir os detalhes do fornecedor em um modal ou em outro lugar da página
        console.log('Detalhes do fornecedor:', fornecedor);
      })
      .catch(error => console.error('Erro ao buscar detalhes do fornecedor:', error));
  }

  function waitForElementToExist(selector, callback, timeout = 2000) {
    const startTime = Date.now();

    function checkElement() {
      const element = document.querySelector(selector);
      if (element) {
        callback(element);
      } else if (Date.now() - startTime < timeout) {
        setTimeout(checkElement, 100);
      } else {
        console.error(`Erro: O elemento ${selector} não foi encontrado após ${timeout}ms.`);
      }
    }

    checkElement();
  }

  window.showDetailsRecorrencia = function (contaId) {
    console.log("Front-end log: showDetailsRecorrencia chamada com contaId:", contaId);
    const detailsRow = document.getElementById(`details-${contaId}`);
    if (!detailsRow) {
      console.error(`Front-end error: Elemento details-${contaId} não encontrado no DOM.`);
      return;
    }
    const parcelasContainer = detailsRow.querySelector('.parcelas-container');
    const iconButton = document.querySelector(`button.btn-details[data-id="${contaId}"] i`);

    // Alterna a exibição da linha de detalhes, mesmo que o ícone não seja encontrado
    if (detailsRow.style.display === 'none') {
      detailsRow.style.display = '';
      if (iconButton) {
        iconButton.classList.remove('fa-plus');
        iconButton.classList.add('fa-minus');
      } else {
        console.error(`Ícone do botão para contaId ${contaId} não foi encontrado.`);
      }
    } else {
      detailsRow.style.display = 'none';
      if (iconButton) {
        iconButton.classList.remove('fa-minus');
        iconButton.classList.add('fa-plus');
      }
      return;
    }

    parcelasContainer.innerHTML = '<p>Carregando...</p>';
    fetch(`/recorrencia_detalhada/${contaId}`)
      .then(response => response.json())
      .then(data => {
        console.log("Dados recebidos do backend:", data);
        parcelasContainer.innerHTML = '';
        if (data.parcelas && data.parcelas.length > 0) {
          let html = '<table class="table table-bordered"><thead><tr><th>Nº Parcela</th><th>Data Vencimento</th><th>Valor</th></tr></thead><tbody>';
          data.parcelas.forEach(parcela => {
            const dataFormatada = formatarDataBR(parcela.data_vencimento);
            html += `
                      <tr>
                          <td>${parcela.numero_da_parcela}/${parcela.total_de_parcelas}</td>
                          <td>${dataFormatada}</td>
                          <td>R$ ${parcela.valor.toFixed(2)}</td>
                      </tr>
                  `;
          });
          html += '</tbody></table>';
          parcelasContainer.innerHTML = html;
        } else {
          parcelasContainer.innerHTML = '<p>Nenhuma parcela encontrada.</p>';
        }
      })
      .catch(error => {
        console.error('Erro ao carregar detalhes:', error);
        parcelasContainer.innerHTML = '<p>Erro ao carregar detalhes.</p>';
      });
  };

  // Nova função para renderizar o código de barras com tooltip e ícone de cópia
  function renderCodigoBarras(texto) {
    if (!texto) return '--';
    if (texto.length <= 12) return texto;
    const curto = texto.substring(0, 12);
    return `<span title="${texto}">${curto}...</span> <span class="copy-icon" style="cursor:pointer;" onclick="copyCodigoBarras('${texto}')"><i class="fa-solid fa-copy"></i></span>`;
  }

  // função para renderizar o email com tooltip e ícone de cópia
  function renderEmail(texto) {
    if (!texto) return '--';
    if (texto.length <= 20) return texto;
    const curto = texto.substring(0, 20);
    return `<span title="${texto}">${curto}...</span> <span class="copy-icon" style="cursor:pointer;" onclick="copyCodigoBarras('${texto}')"><i class="fa-solid fa-copy"></i></span>`;
  }

  // Nova função para renderizar a empresa pagadora com tooltip
  function renderTexto(texto) {
    if (!texto) return '--';
    if (texto.length <= 20) return texto;
    const curto = texto.substring(0, 20);
    return `<span title="${texto}">${curto}...</span>`;
  }

  // Função para caso a data esteja vazia, coloque '--'
  function renderData(data) {
    if (!data) return '--';
    return data;
  }

  // Função para encurtar o link do boletos
  function renderBoletoLink(filename) {
    if (!filename) return '--';
    return `<a href="${filename}" target="_blank" class="btn btn-outline-primary btn-sm">
              <i class="fa-solid fa-file-pdf"></i> Ver
            </a>`;
  }

  // Função para formatar valor para BRL (atualizada)
  function formatarValor(valor) {
    return Number(valor).toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
  }

  // Função para copiar o texto completo para a área de transferência
  function copyCodigoBarras(texto) {
    navigator.clipboard.writeText(texto)
      .then(() => {
        alert('Código de barras copiado!');
        console.log('Código de barras copiado!');
      })
      .catch(err => console.error('Erro ao copiar:', err));
  }
  // Torna a função globalmente acessível para uso via onclick
  window.copyCodigoBarras = copyCodigoBarras;

  // Função para formatar data para SQL
  function formatDateForSQL(dateValue) {
    // Se já estiver no formato ISO (YYYY-MM-DD) retorne-o diretamente
    if (dateValue.includes('-')) {
      return dateValue;
    }
    const [day, month, year] = dateValue.split('/');
    return `${year}-${month}-${day}`;
  }

  // Função para somar a coluna "Valor" e exibir o resultado em #total-valor
  function atualizarSomaValor() {
    let soma = 0;
    document.querySelectorAll("#contas-list tr").forEach(row => {
      const valorCelula = row.querySelector("td:nth-child(4)");
      if (valorCelula) {
        const texto = valorCelula.textContent.replace(/[^\d,.-]/g, "").replace(",", ".");
        const numero = parseFloat(texto);
        if (!isNaN(numero)) soma += numero;
      }
    });
    document.getElementById("total-valor").textContent = soma.toLocaleString(
      "pt-BR",
      { style: "currency", currency: "BRL" }
    );
  }

  function atualizarSomaValorPagamentos() {
    let soma = 0;
    document.querySelectorAll("#contas-list tr").forEach(row => {
      const valorCelula = row.querySelector("td:nth-child(4)");
      if (valorCelula) {
        const texto = valorCelula.textContent
          .replace("R$", "")
          .replace(/\s+/g, "")
          .replace(/\./g, "")
          .replace(",", ".");

        const numero = parseFloat(texto);
        if (!isNaN(numero)) soma += numero;
      }
    });
    document.getElementById("total-valor-pg").textContent =
      soma.toLocaleString("pt-BR", { style: "currency", currency: "BRL" });
  }

  // Inicializa eventos ao carregar a página
  initializePageEvents();

  document.addEventListener('click', function (event) {
    const button = event.target.closest('.btn-details');
    if (button) {
      event.preventDefault();
      const contaId = button.getAttribute('data-id');
      showDetailsRecorrencia(contaId);
    }
  });
});
