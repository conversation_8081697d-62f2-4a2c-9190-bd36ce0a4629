# Funcionalidade de Substituição de Comprovantes de Pagamento

## Resumo da Implementação

Esta funcionalidade permite que os usuários substituam comprovantes de pagamento já enviados de forma simples e direta, sem a necessidade de editar outros dados do pagamento.

## Problemas Resolvidos

### Problema 1 - Botão redundante
- **Antes**: Havia um botão de substituição redundante na tabela que abria o mesmo modal de edição
- **Solução**: Criado um modal específico para substituição, acessível através de um botão "Substituir" ao lado do botão "Ver" do comprovante

### Problema 2 - Modal inadequado
- **Antes**: O modal de edição exigia campos obrigatórios desnecessários para substituição
- **Solução**: Criado modal dedicado que solicita apenas o novo arquivo

## Funcionalidades Implementadas

### 1. Modal Específico de Substituição
- **Localização**: Criado dinamicamente via JavaScript
- **ID**: `modalSubstituirComprovante`
- **Características**:
  - Mostra o comprovante atual com link para visualização
  - Campo de upload para novo arquivo
  - Validação de formatos (PDF, JPG, JPEG, PNG)
  - Limite de tamanho: 10MB
  - Confirmação antes da substituição

### 2. Botão de Substituição
- **Localização**: Ao lado do botão "Ver" na coluna de comprovantes
- **Aparência**: Botão amarelo com ícone de rotação
- **Texto**: "Substituir"
- **Funcionalidade**: Abre o modal específico de substituição

### 3. Validações
- **Arquivo obrigatório**: Não permite substituição sem selecionar arquivo
- **Formatos aceitos**: PDF, JPG, JPEG, PNG
- **Confirmação**: Solicita confirmação antes de executar a substituição
- **Feedback**: Mensagens específicas de sucesso/erro

## Arquivos Modificados

### 1. `static/js/contas_home.js`
- **Função adicionada**: `renderComprovanteLink()` - Renderiza comprovante com botão de substituição
- **Função adicionada**: `openSubstituirComprovanteModal()` - Abre modal específico
- **Função adicionada**: `handleSubstituirComprovante()` - Processa a substituição
- **Função adicionada**: `addSubstituirComprovanteEventListeners()` - Adiciona eventos aos botões
- **Função modificada**: `updatePagamentosTable()` - Usa nova função de renderização
- **Função simplificada**: `openEditModalPagamento()` - Removidas funcionalidades de substituição

### 2. `templates/contas_pagamentos.html`
- **CSS adicionado**: Estilos para botões de substituição e modal
- **Elemento removido**: Div de informações sobre comprovante existente (não mais necessária)

## Fluxo de Funcionamento

### 1. Visualização
1. Usuário acessa a aba de Pagamentos
2. Na coluna "Comprovante", vê botões "Ver" e "Substituir" (quando há comprovante)
3. Botão "Substituir" só aparece quando existe comprovante

### 2. Substituição
1. Usuário clica em "Substituir"
2. Modal específico abre mostrando:
   - Link para ver comprovante atual
   - Campo para selecionar novo arquivo
   - Informações sobre formatos aceitos
3. Usuário seleciona novo arquivo
4. Clica em "Substituir Comprovante"
5. Sistema solicita confirmação
6. Após confirmação, arquivo é enviado
7. Comprovante é substituído no banco de dados
8. Data de pagamento é atualizada para a data atual
9. Tabela é atualizada automaticamente
10. Mensagem de sucesso é exibida

## Comportamento da Data de Pagamento

### Atualização Automática
Quando um comprovante é substituído, a data de pagamento é automaticamente atualizada para a data atual (data da substituição). Este comportamento garante que:

1. **Consistência**: A data reflete quando o comprovante foi realmente anexado
2. **Rastreabilidade**: É possível saber quando a última alteração foi feita
3. **Transparência**: O usuário é informado sobre esta atualização antes de confirmar

### Informações ao Usuário
- **Modal**: Aviso de que a data será atualizada para a data atual
- **Confirmação**: Mostra a data que será definida (formato brasileiro)
- **Sucesso**: Confirma que tanto comprovante quanto data foram atualizados

### Formato da Data
- **Frontend**: Formato brasileiro (dd/mm/aaaa) para exibição
- **Backend**: Formato ISO (aaaa-mm-dd) para processamento
- **Banco de dados**: Armazenado conforme estrutura existente

## Aspectos Técnicos

### Backend
- **Endpoint utilizado**: `/update_pagamento/<recorrencia_id>` (PATCH)
- **Parâmetros enviados**: `file` e `data_pagamentos` (data atual)
- **Função do banco**: `atualizar_pagamento()` - Aceita parâmetros opcionais
- **Upload**: Utiliza função existente `upload_pagamentos_s3()`

### Frontend
- **Requisição**: FormData com arquivo, recorrencia_id e data_pagamentos (data atual)
- **Validação**: Arquivo obrigatório para substituição
- **Atualização**: Recarrega tabela automaticamente após sucesso
- **UX**: Loading durante processo, mensagens de feedback específicas
- **Data**: Atualiza automaticamente a data de pagamento para a data atual

## Vantagens da Implementação

1. **Simplicidade**: Processo direto sem campos desnecessários
2. **Consistência**: Atualiza data de pagamento junto com o comprovante
3. **UX**: Interface clara e intuitiva com feedback específico
4. **Reutilização**: Aproveita infraestrutura existente
5. **Validação**: Mantém todas as validações de arquivo
6. **Transparência**: Informa claramente que a data será atualizada

## Compatibilidade

- **Navegadores**: Compatível com navegadores modernos
- **Mobile**: Responsivo através do Bootstrap
- **Backend**: Utiliza endpoints existentes
- **Banco de dados**: Não requer alterações na estrutura

## Testes Recomendados

1. **Substituição bem-sucedida**: Arquivo válido
2. **Validação de formato**: Arquivos inválidos
3. **Validação de tamanho**: Arquivos muito grandes
4. **Cancelamento**: Fechar modal sem substituir
5. **Erro de rede**: Falha na comunicação
6. **Permissões**: Diferentes níveis de usuário
7. **Atualização da interface**: Verificar se tabela atualiza
8. **Múltiplas substituições**: Substituir várias vezes o mesmo comprovante
