# Funcionalidade de Substituição de Comprovantes de Pagamento

## Resumo da Implementação

Esta funcionalidade permite que os usuários substituam comprovantes de pagamento já enviados de forma simples e direta, sem a necessidade de editar outros dados do pagamento.

## Problemas Resolvidos

### Problema 1 - Botão redundante
- **Antes**: Havia um botão de substituição redundante na tabela que abria o mesmo modal de edição
- **Solução**: Criado um modal específico para substituição, acessível através de um botão "Substituir" ao lado do botão "Ver" do comprovante

### Problema 2 - Modal inadequado
- **Antes**: O modal de edição exigia campos obrigatórios desnecessários para substituição
- **Solução**: Criado modal dedicado que solicita apenas o novo arquivo

## Funcionalidades Implementadas

### 1. Modal Específico de Substituição
- **Localização**: Criado dinamicamente via JavaScript
- **ID**: `modalSubstituirComprovante`
- **Características**:
  - Mostra o comprovante atual com link para visualização
  - Campo de upload para novo arquivo
  - **Campo de data opcional** pré-preenchido com data atual
  - Validação de formatos (PDF, JPG, JPEG, PNG)
  - Limite de tamanho: 10MB
  - Confirmação antes da substituição com data específica

### 2. Botão de Substituição
- **Localização**: Ao lado do botão "Ver" na coluna de comprovantes
- **Aparência**: Botão amarelo com ícone de rotação
- **Texto**: "Substituir"
- **Funcionalidade**: Abre o modal específico de substituição

### 3. Validações
- **Arquivo obrigatório**: Não permite substituição sem selecionar arquivo
- **Formatos aceitos**: PDF, JPG, JPEG, PNG
- **Validação de data**: Verifica se a data inserida é válida (formato ISO)
- **Fallback de data**: Usa data atual se campo estiver vazio ou inválido
- **Confirmação**: Solicita confirmação mostrando a data que será utilizada
- **Feedback**: Mensagens específicas de sucesso/erro com data confirmada

## Arquivos Modificados

### 1. `static/js/contas_home.js`
- **Função adicionada**: `renderComprovanteLink()` - Renderiza comprovante com botão de substituição
- **Função adicionada**: `openSubstituirComprovanteModal()` - Abre modal específico
- **Função adicionada**: `handleSubstituirComprovante()` - Processa a substituição
- **Função adicionada**: `addSubstituirComprovanteEventListeners()` - Adiciona eventos aos botões
- **Função modificada**: `updatePagamentosTable()` - Usa nova função de renderização
- **Função simplificada**: `openEditModalPagamento()` - Removidas funcionalidades de substituição

### 2. `templates/contas_pagamentos.html`
- **CSS adicionado**: Estilos para botões de substituição e modal
- **Elemento removido**: Div de informações sobre comprovante existente (não mais necessária)

## Fluxo de Funcionamento

### 1. Visualização
1. Usuário acessa a aba de Pagamentos
2. Na coluna "Comprovante", vê botões "Ver" e "Substituir" (quando há comprovante)
3. Botão "Substituir" só aparece quando existe comprovante

### 2. Substituição
1. Usuário clica em "Substituir"
2. Modal específico abre mostrando:
   - Link para ver comprovante atual
   - Campo para selecionar novo arquivo
   - **Campo de data pré-preenchido com data atual**
   - Informações sobre formatos aceitos
3. Usuário seleciona novo arquivo
4. **Usuário pode alterar a data de pagamento (opcional)**
5. Clica em "Substituir Comprovante"
6. Sistema solicita confirmação **mostrando a data que será utilizada**
7. Após confirmação, arquivo e data são enviados
8. Comprovante é substituído no banco de dados
9. Data de pagamento é atualizada para a data escolhida
10. Tabela é atualizada automaticamente
11. Mensagem de sucesso é exibida **com a data confirmada**

## Comportamento da Data de Pagamento

### Campo de Data Opcional
O modal de substituição agora inclui um campo de data que permite ao usuário escolher quando o pagamento foi realizado:

1. **Pré-preenchimento**: Campo vem automaticamente preenchido com a data atual
2. **Flexibilidade**: Usuário pode alterar para qualquer data desejada
3. **Fallback**: Se campo estiver vazio ou inválido, usa a data atual automaticamente
4. **Validação**: Verifica formato de data (YYYY-MM-DD) antes do envio

### Informações ao Usuário
- **Modal**: Campo de data visível e editável
- **Confirmação**: Mostra exatamente qual data será utilizada (formato brasileiro)
- **Sucesso**: Confirma a data específica que foi definida

### Formato da Data
- **Frontend**: Formato brasileiro (dd/mm/aaaa) para exibição
- **Backend**: Formato ISO (aaaa-mm-dd) para processamento
- **Banco de dados**: Armazenado conforme estrutura existente

## Aspectos Técnicos

### Backend
- **Endpoint utilizado**: `/update_pagamento/<recorrencia_id>` (PATCH)
- **Parâmetros enviados**: `file` e `data_pagamentos` (data atual)
- **Função do banco**: `atualizar_pagamento()` - Aceita parâmetros opcionais
- **Upload**: Utiliza função existente `upload_pagamentos_s3()`

### Frontend
- **Requisição**: FormData com arquivo, recorrencia_id e data_pagamentos (data escolhida)
- **Validação**: Arquivo obrigatório, validação de formato de data
- **Atualização**: Recarrega tabela automaticamente após sucesso
- **UX**: Loading durante processo, mensagens de feedback específicas
- **Data**: Campo editável com fallback para data atual
- **Confirmação**: Mostra data específica que será utilizada

## Vantagens da Implementação

1. **Simplicidade**: Processo direto com apenas os campos necessários
2. **Flexibilidade**: Permite escolher data específica ou usar data atual
3. **UX**: Interface clara e intuitiva com feedback específico
4. **Reutilização**: Aproveita infraestrutura existente
5. **Validação**: Mantém todas as validações de arquivo e adiciona validação de data
6. **Transparência**: Informa claramente qual data será utilizada

## Compatibilidade

- **Navegadores**: Compatível com navegadores modernos
- **Mobile**: Responsivo através do Bootstrap
- **Backend**: Utiliza endpoints existentes
- **Banco de dados**: Não requer alterações na estrutura

## Testes Recomendados

1. **Substituição bem-sucedida**: Arquivo válido
2. **Validação de formato**: Arquivos inválidos
3. **Validação de tamanho**: Arquivos muito grandes
4. **Cancelamento**: Fechar modal sem substituir
5. **Erro de rede**: Falha na comunicação
6. **Permissões**: Diferentes níveis de usuário
7. **Atualização da interface**: Verificar se tabela atualiza
8. **Múltiplas substituições**: Substituir várias vezes o mesmo comprovante
9. **Validação de data**: Testar datas inválidas e campos vazios
10. **Diferentes datas**: Testar com datas passadas, futuras e atual

## Como Usar

### 📋 **Fluxo de Substituição:**

1. Acesse a aba "Pagamentos"
2. Localize um pagamento com comprovante existente
3. Clique no botão "Substituir" ao lado de "Ver"
4. Selecione o novo arquivo no modal
5. **Opcionalmente, altere a data de pagamento** (pré-preenchida com data atual)
6. Confirme a substituição (sistema mostra qual data será utilizada)
7. O comprovante e data serão atualizados automaticamente

### 🎯 **Dicas de Uso:**

- **Data padrão**: O campo vem pré-preenchido com a data atual
- **Flexibilidade**: Você pode alterar para qualquer data necessária
- **Validação**: Se inserir uma data inválida, o sistema usará a data atual
- **Confirmação**: Sempre verifique a data mostrada na confirmação
- **Feedback**: A mensagem de sucesso confirma a data utilizada
