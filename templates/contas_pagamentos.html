<!DOCTYPE html>
<html lang="pt">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contas Home</title>
    <!-- AdminLTE CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/css/adminlte.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.0/css/all.min.css">
    <!-- Select2 CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>

    </style>
</head>

<body class="hold-transition sidebar-mini">
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="container">
                <!-- Header -->
                <section class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1>Contas Home</h1>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Main Content -->
                <section class="content">
                    <div class="container-fluid">
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">Conciliação de Pagamentos</h3>
                            </div>
                            <div class="card-body">
                                <div class="card mt-3">
                                    <div class="card-body">
                                        <div class="info-box bg-gradient-warning" style="width: 15%;">
                                            <span class="info-box-icon"><i class="fa-solid fa-money-bill"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">Total à Pagar</span>
                                                <span class="info-box-number" id="total-valor-pg"></span>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label for="filtro-periodo-pg">Filtrar por Período</label>
                                                <input type="text" class="form-control" id="filtro-periodo-pg"
                                                    placeholder="Selecione o período"
                                                    style="background-color: #fff !important;">
                                            </div>
                                            <div class="col-md-4">
                                                <label for="filtro-empresa-pg">Filtrar por Empresa Pagadora</label>
                                                <select class="form-control" id="filtro-empresa-pg">
                                                    <option value="">Todas Empresas</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label for="filtro-fornecedor-pg">Filtrar por Fornecedor</label>
                                                <select class="form-control" id="filtro-fornecedor-pg">
                                                    <option value="">Todos Fornecedores</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <div class="info-box bg-gradient-warning">
                                                    <span class="info-box-icon"><i class="fa-solid fa-money-bill"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Total a Pagar</span>
                                                        <span class="info-box-number" id="total-a-pagar">R$ 0,00</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="info-box bg-gradient-success">
                                                    <span class="info-box-icon"><i class="fa-solid fa-check"></i></span>
                                                    <div class="info-box-content">
                                                        <span class="info-box-text">Total Pago</span>
                                                        <span class="info-box-number" id="total-pago">R$ 0,00</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Nome da Conta</th>
                                                        <th>Empresa Pagadora</th>
                                                        <th>Fornecedor/Funcionário</th>
                                                        <th>Valor</th>
                                                        <th>Data de Vencimento</th>
                                                        <th>Data de Pagamento</th>
                                                        <th>Comprovante</th>
                                                        <th>Ações</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="contas-list">
                                                    <!-- Conteúdo das contas -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Editar Conta -->
                                <div class="modal fade" id="modalEditarPagamento" tabindex="-1" role="dialog"
                                    aria-labelledby="modalEditarPagamentoLabel" aria-hidden="true">
                                    <div class="modal-dialog" role="document" style="max-width: 70%;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="modalEditarPagamentoLabel">Editar Pagamento
                                                </h5>
                                                <button type="button" class="close" data-dismiss="modal"
                                                    aria-label="Fechar">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="form-editar-conta">
                                                    <input type="hidden" id="editar-conta-id">
                                                    <input type="hidden" id="editar-recorrencia-id">
                                                    <div class="form-group">
                                                        <label for="editar-codigo-pagamento">Código de Barras</label>
                                                        <input type="text" class="form-control"
                                                            id="editar-codigo-pagamento" name="codigo" readonly>
                                                    </div>

                                                    <div class="row" id="info-pagamento"
                                                        style="display: flex; flex-wrap: wrap; margin-right: -7.5px; margin-left: -7.5px;">
                                                        <div class="col-md-6">
                                                            <label for="editar-banco-pagamento">Banco</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-banco-pagamento" name="banco" readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-codBanco-pagamento">Código do
                                                                Banco</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-codBanco-pagamento" name="codBanco" readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-agencia-pagamento">Agência</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-agencia-pagamento" name="agencia" readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-codAgencia-pagamento">Dígito da
                                                                Agência</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-codAgencia-pagamento" name="codAgencia"
                                                                readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-tipoConta-pagamento">Tipo da
                                                                Conta</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-tipoConta-pagamento" name="tipoConta"
                                                                readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-conta-pagamento">Conta</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-conta-pagamento" name="conta" readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-codConta-pagamento">Dígito da
                                                                Conta</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-codConta-pagamento" name="codConta" readonly>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <label for="editar-documento-pagamento">Documento</label>
                                                            <input type="text" class="form-control"
                                                                id="editar-documento-pagamento" name="documento"
                                                                readonly>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label for="editar-data-pagamento">Data de Pagamento</label>
                                                        <input type="date" class="form-control date-picker"
                                                            id="editar-data-pagamento" name="data_pagamento" required>
                                                    </div>

                                                    <div class="form-group">
                                                        <label for="editar-comprovante-pagamento">Comprovante</label>
                                                        <div class="custom-file">
                                                            <input type="file" class="custom-file-input"
                                                                id="editar-comprovante-pagamento" name="comprovante">
                                                            <label class="custom-file-label"
                                                                for="editar-comprovante-pagamento">Escolher
                                                                arquivo</label>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary"
                                                    id="btnCancelarModalPagamento"
                                                    data-dismiss="modal">Cancelar</button>
                                                <button id="salvar-editar-pagamento"
                                                    class="btn btn-primary">Salvar</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.1/dist/js/adminlte.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/pt.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            $('.select2').select2({
                placeholder: "Selecione uma opção",
                allowClear: true
            });

            // Inicializa Flatpickr
            flatpickr(".date-picker", {
                locale: "pt",
                dateFormat: "d/m/Y"
            });

            // Selecionar "Única" por padrão
            document.getElementById('recorrencia-unica').checked = true;

            const fileInput = document.getElementById('editar-comprovante');
            fileInput.addEventListener('change', function () {
                const fileName = this.files[0]?.name || 'Nenhum arquivo selecionado';
                this.nextElementSibling.innerText = fileName;
            });
        });
    </script>
    <script src="/static/js/contas_home.js"></script>
</body>

</html>